/**
 * @file main.cpp
 * @brief TCP消息总线服务器主程序
 * <AUTHOR>
 * @date 2025-08-24
 */

#include <signal.h>
#include <spdlog/spdlog.h>

#include <atomic>
#include <chrono>
#include <iostream>
#include <memory>
#include <thread>

#include "zexuan/base/logger_manager.hpp"
#include "zexuan/net/event_loop.hpp"
#include "zexuan/bus/tcp_bus_server.hpp"

using namespace zexuan::net;
using namespace zexuan::bus;
using namespace zexuan::base;

// 全局变量
std::atomic<bool> g_running{true};
std::shared_ptr<TcpBusServer> g_message_bus;
std::shared_ptr<EventLoop> g_event_loop;

// 信号处理函数
void signalHandler(int signal) {
    // 在信号处理函数中只设置标志，不直接操作复杂对象
    g_running.store(false);

    // 让事件循环退出
    if (g_event_loop) {
        g_event_loop->quit();
    }
}

// 打印统计信息
void printStatistics(const TcpBusServer& bus) {
    auto sub_stats = bus.getSubscriptionStatistics();
    auto routing_stats = bus.getRoutingStatistics();
    
    spdlog::info("=== TCP Message Bus Statistics ===");
    spdlog::info("Connections: {}", bus.getConnectionCount());
    spdlog::info("Total Clients: {}", sub_stats.total_clients);
    spdlog::info("Message Subscriptions: {}", sub_stats.total_message_subscriptions);
    spdlog::info("Event Subscriptions: {}", sub_stats.total_event_subscriptions);
    spdlog::info("Messages Routed: {}", routing_stats.total_messages_routed);
    spdlog::info("Common Messages: {}", routing_stats.common_messages_routed);
    spdlog::info("Event Messages: {}", routing_stats.event_messages_routed);
    spdlog::info("Control Messages: {}", routing_stats.control_messages_sent);
    spdlog::info("Routing Errors: {}", routing_stats.routing_errors);
    spdlog::info("No Subscribers: {}", routing_stats.no_subscribers_count);
    spdlog::info("==================================");
}

int main(int argc, char* argv[]) {
    // 初始化日志系统
    std::string config_path = "./config/config.json";
    if (argc > 1) {
        config_path = argv[1];
    }
    
    if (!LoggerManager::initialize("tcp_bus_server.log", config_path)) {
        std::cerr << "Failed to initialize logger" << std::endl;
        return -1;
    }
    
    spdlog::info("TCP Message Bus Server starting...");
    spdlog::info("Using config file: {}", config_path);
    
    // 注册信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    try {
        // 创建事件循环
        g_event_loop = std::make_shared<EventLoop>();

        // 创建TCP消息总线
        g_message_bus = std::make_shared<TcpBusServer>(g_event_loop.get(), config_path);
        
        // 启动消息总线
        if (!g_message_bus->start()) {
            spdlog::error("Failed to start TCP message bus");
            return -1;
        }
        
        spdlog::info("TCP Message Bus started successfully");
        
        // 启动统计信息打印线程
        std::thread stats_thread([&]() {
            while (g_running.load()) {
                std::this_thread::sleep_for(std::chrono::seconds(30));
                if (g_running.load() && g_message_bus) {
                    printStatistics(*g_message_bus);
                    
                    // 清理无效连接
                    size_t cleaned = g_message_bus->cleanupInvalidConnections();
                    if (cleaned > 0) {
                        spdlog::info("Cleaned up {} invalid connections", cleaned);
                    }
                }
            }
        });
        
        // 运行事件循环（阻塞直到quit()被调用）
        g_event_loop->loop();

        spdlog::info("TCP Message Bus event loop stopped");

        // 等待统计线程结束
        if (stats_thread.joinable()) {
            stats_thread.join();
        }

        // 确保完全清理消息总线
        if (g_message_bus) {
            printStatistics(*g_message_bus);
            g_message_bus->stop();
            g_message_bus.reset();
        }

        // 清理事件循环
        g_event_loop.reset();
        
    } catch (const std::exception& e) {
        spdlog::error("Exception in main: {}", e.what());
        return -1;
    }
    
    spdlog::info("TCP Message Bus Server shutdown completed");
    return 0;
}
