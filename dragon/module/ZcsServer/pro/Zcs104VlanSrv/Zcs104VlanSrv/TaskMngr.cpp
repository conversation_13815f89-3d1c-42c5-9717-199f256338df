#include "TaskMngr.h"

#include "SecDevFlowModule.h"

/*************************************************************
 函 数 名:   CTaskMngr
 功能概要:   构造函数
 返 回 值:   无
**************************************************************/
CTaskMngr::CTaskMngr()
:m_LockForRcvBusInfList("TaskSttpTrans")
{
	m_pExit	= NULL;
    m_pLogFile	= NULL;
    mapStnLinkCfg.clear();
    map102Push.clear();
    map102PushTheadId.clear();

    // lmy add
    map103Push.clear();
    map103PushTheadId.clear();

    //p_102Push =NULL;
    m_strSftpHomePath = "/home/<USER>";
    m_strComtradePath = "/home/<USER>/var/zexin/zxdown/";
    m_strScdPath = "/SCD/station.zip";
    m_pDBAcess = NULL;
    m_StnRun.clear();
    m_mapStnMgrId.clear();
    
   
    m_mapFntSta.clear() ;
    m_nModFileCallType = 0;
    m_workArea = 2;
    m_nLinkStatus = -1;

    // 初始化服务器在线管理器相关变量
    m_pSrvOnlineMngr = NULL;
    m_bSrvOnlineMngrEnabled = false;
}
/*************************************************************
 函 数 名:   ~CTaskMngr
 功能概要:	 析构函数
 返 回 值:   无
**************************************************************/
CTaskMngr::~CTaskMngr()
{
	
}


bool CTaskMngr::Start(bool * pExit)
{
	m_pExit = pExit;//m_pExit 的地址和 传入的bExit的地址一样，一起变化
    
	if (!Init()) {
        if(m_pLogFile==NULL){
            printf("[CTaskMngr::Start()] Init()失败\n");
        }else{
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Start()] Init()失败");
        }
		return false;
	}
    
    
    PreAPCI();
//    {
//        
//        CMMSAnalyze cAnalyze;
//        cAnalyze.Init(m_pLogFile);
//        string strerrTpkt;
//        cAnalyze.Pack_ServiceEorr(5686,strerrTpkt);
//        printf("[CTaskMngr::Start()]strerrTpkt \n");
//        PrintBytes(strerrTpkt);
//    }
//    {
//        string strRef="PM5002ALD0/LLN0$BR$brcbRelayDin06";
//        DomainSpecific sDomain;
//        std::size_t pos = strRef.find("/");  // PL2201PORT/LDHD1$DC$PhyNam$swRev
//        if(pos!=std::string::npos){
//            sDomain.DomainID = strRef.substr(0,pos);//PL2201PORT
//            sDomain.ItemID = strRef.substr(pos+1);//LDHD1$DC$PhyNam$swRev
//        }
//
//        if(sDomain.DomainID.empty()||sDomain.ItemID.empty()){
//            printf("[sttp2MMs40005()] 参引[%s] strDomainId[%s] strItem[%s] 失败\n",strRef.c_str(),sDomain.DomainID.c_str(),sDomain.ItemID.c_str());
//        }else{
//            printf("[sttp2MMs40005()] 参引[%s] strDomainId[%s] strItem[%s] 成功\n",strRef.c_str(),sDomain.DomainID.c_str(),sDomain.ItemID.c_str());
//        }
//        List_Domain listDomain;
//        listDomain.push_back(sDomain);
//        
//        CMMSAnalyze cAnalyze;
//        cAnalyze.Init(m_pLogFile);
//        string strReadMMsTpkt;
//        cAnalyze.Pack_Read(5685,listDomain,strReadMMsTpkt);
//        printf("[CTaskMngr::Start()] strDomainId[%s] strItem[%s]\n",sDomain.DomainID.c_str(),sDomain.ItemID.c_str());
//        PrintBytes(strReadMMsTpkt);
//    }

//    {
//        char c100[]={0x03,0x00,0x04,0x04,0x02,0xf0,0x00,0x01,0x00,0x01,0x00,0x61,0x82,0x0e,0x99,0x30,0x82,0x0e,0x95,0x02,0x01,0x03,0xa0,0x82,0x0e,0x8e,0xa0,0x82,0x0e,0x8a,0x02,0x02,0x1e,0x1d,0xa4,0x82,0x0e,0x82,0xa1,0x82,0x0e,0x7e,0xa0,0x82,0x0e,0x7a,0x30,0x2d,0xa0,0x2b,0xa1,0x29,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x1a,0x4c,0x50,0x48,0x44,0x31,0x24,0x53,0x50,0x24,0x53,0x65,0x74,0x74,0x69,0x6e,0x67,0x47,0x72,0x70,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2d,0xa0,0x2b,0xa1,0x29,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x1a,0x4c,0x50,0x48,0x44,0x31,0x24,0x53,0x50,0x24,0x44,0x65,0x76,0x69,0x63,0x65,0x4e,0x61,0x6d,0x65,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x29,0xa0,0x27,0xa1,0x25,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x16,0x54,0x43,0x54,0x52,0x31,0x24,0x53,0x50,0x24,0x41,0x52,0x74,0x67,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x54,0x43,0x54,0x52,0x31,0x24,0x53,0x50,0x24,0x41,0x52,0x74,0x67,0x53,0x6e,0x64,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x29,0xa0,0x27,0xa1,0x25,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x16,0x54,0x56,0x54,0x52,0x31,0x24,0x53,0x50,0x24,0x56,0x52,0x74,0x67,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x27,0xa0,0x25,0xa1,0x23,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x14,0x50,0x53,0x43,0x48,0x31,0x24,0x53,0x50,0x24,0x54,0x79,0x70,0x65,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x4c,0x4c,0x4e,0x30,0x24,0x53,0x47,0x24,0x44,0x50,0x46,0x43,0x53,0x74,0x72,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2a,0xa0,0x28,0xa1,0x26,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x17,0x4c,0x4c,0x4e,0x30,0x24,0x53,0x47,0x24,0x52,0x4f,0x43,0x53,0x74,0x72,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2d,0xa0,0x2b,0xa1,0x29,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x1a,0x50,0x44,0x49,0x46,0x31,0x24,0x53,0x47,0x24,0x53,0x74,0x72,0x56,0x61,0x6c,0x53,0x47,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x53,0x43,0x48,0x31,0x24,0x53,0x47,0x24,0x4c,0x6f,0x63,0x43,0x68,0x6e,0x49,0x44,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x53,0x43,0x48,0x31,0x24,0x53,0x47,0x24,0x52,0x65,0x6d,0x43,0x68,0x6e,0x49,0x44,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2e,0xa0,0x2c,0xa1,0x2a,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x1b,0x50,0x44,0x49,0x53,0x31,0x24,0x53,0x47,0x24,0x5a,0x50,0x73,0x4d,0x61,0x67,0x56,0x61,0x6c,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x44,0x49,0x53,0x31,0x24,0x53,0x47,0x24,0x4c,0x69,0x6e,0x41,0x6e,0x67,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2f,0xa0,0x2d,0xa1,0x2b,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x1c,0x50,0x44,0x49,0x53,0x31,0x24,0x53,0x47,0x24,0x5a,0x5a,0x65,0x72,0x4d,0x61,0x67,0x56,0x61,0x6c,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x50,0x44,0x49,0x53,0x31,0x24,0x53,0x47,0x24,0x4c,0x69,0x6e,0x41,0x6e,0x67,0x30,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2d,0xa0,0x2b,0xa1,0x29,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x1a,0x50,0x44,0x49,0x46,0x31,0x24,0x53,0x47,0x24,0x4c,0x69,0x6e,0x43,0x61,0x70,0x61,0x63,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2e,0xa0,0x2c,0xa1,0x2a,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x1b,0x50,0x44,0x49,0x46,0x31,0x24,0x53,0x47,0x24,0x4c,0x69,0x6e,0x43,0x61,0x70,0x61,0x63,0x30,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2d,0xa0,0x2b,0xa1,0x29,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x1a,0x52,0x46,0x4c,0x4f,0x31,0x24,0x53,0x47,0x24,0x4c,0x69,0x6e,0x4c,0x65,0x6e,0x4b,0x6d,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x44,0x49,0x53,0x31,0x24,0x53,0x47,0x24,0x47,0x6e,0x64,0x53,0x74,0x72,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x44,0x49,0x53,0x35,0x24,0x53,0x47,0x24,0x47,0x6e,0x64,0x53,0x74,0x72,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x50,0x44,0x49,0x53,0x35,0x24,0x53,0x47,0x24,0x47,0x6e,0x64,0x44,0x6c,0x54,0x6d,0x6d,0x73,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x44};
//        char c101[]={0x03,0x00,0x04,0x04,0x02,0xf0,0x00,0x49,0x53,0x38,0x24,0x53,0x47,0x24,0x47,0x6e,0x64,0x53,0x74,0x72,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x50,0x44,0x49,0x53,0x38,0x24,0x53,0x47,0x24,0x47,0x6e,0x64,0x44,0x6c,0x54,0x6d,0x6d,0x73,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2a,0xa0,0x28,0xa1,0x26,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x17,0x50,0x44,0x49,0x53,0x31,0x24,0x53,0x47,0x24,0x50,0x68,0x53,0x74,0x72,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2a,0xa0,0x28,0xa1,0x26,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x17,0x50,0x44,0x49,0x53,0x36,0x24,0x53,0x47,0x24,0x50,0x68,0x53,0x74,0x72,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x44,0x49,0x53,0x36,0x24,0x53,0x47,0x24,0x50,0x68,0x44,0x6c,0x54,0x6d,0x6d,0x73,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2a,0xa0,0x28,0xa1,0x26,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x17,0x50,0x44,0x49,0x53,0x39,0x24,0x53,0x47,0x24,0x50,0x68,0x53,0x74,0x72,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x44,0x49,0x53,0x39,0x24,0x53,0x47,0x24,0x50,0x68,0x44,0x6c,0x54,0x6d,0x6d,0x73,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x44,0x49,0x53,0x31,0x24,0x53,0x47,0x24,0x52,0x69,0x73,0x4c,0x6f,0x64,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x54,0x4f,0x43,0x31,0x24,0x53,0x47,0x24,0x53,0x74,0x72,0x56,0x61,0x6c,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x54,0x4f,0x43,0x31,0x24,0x53,0x47,0x24,0x4f,0x70,0x44,0x6c,0x54,0x6d,0x6d,0x73,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x54,0x4f,0x43,0x32,0x24,0x53,0x47,0x24,0x53,0x74,0x72,0x56,0x61,0x6c,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x54,0x4f,0x43,0x32,0x24,0x53,0x47,0x24,0x4f,0x70,0x44,0x6c,0x54,0x6d,0x6d,0x73,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x54,0x4f,0x43,0x33,0x24,0x53,0x47,0x24,0x53,0x74,0x72,0x56,0x61,0x6c,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x54,0x4f,0x43,0x36,0x24,0x53,0x47,0x24,0x53,0x74,0x72,0x56,0x61,0x6c,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x54,0x4f,0x43,0x35,0x24,0x53,0x47,0x24,0x53,0x74,0x72,0x56,0x61,0x6c,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x54,0x4f,0x43,0x35,0x24,0x53,0x47,0x24,0x4f,0x70,0x44,0x6c,0x54,0x6d,0x6d,0x73,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x52,0x52,0x45,0x43,0x31,0x24,0x53,0x47,0x24,0x53,0x50,0x52,0x65,0x63,0x54,0x6d,0x6d,0x73,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x52,0x52,0x45,0x43,0x31,0x24,0x53,0x47,0x24,0x54,0x50,0x52,0x65,0x63,0x54,0x6d,0x6d,0x73,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2e,0xa0,0x2c,0xa1,0x2a,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x1b,0x52,0x52,0x45,0x43,0x31,0x24,0x53,0x47,0x24,0x52,0x65,0x63,0x44,0x69,0x66,0x41,0x6e,0x67,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x50,0x44,0x49,0x46,0x31,0x24,0x53,0x47,0x24,0x4c,0x6f,0x63,0x53,0x68,0x52,0x58,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x44,0x49,0x46,0x31,0x24,0x53,0x47,0x24,0x4c,0x6f,0x63,0x4e,0x52,0x58,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x44,0x49,0x46,0x31,0x24,0x53,0x47,0x24,0x43,0x54,0x46,0x61,0x63,0x74,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x50,0x50,0x44,0x50,0x31,0x24,0x53,0x47,0x24,0x41,0x42,0x6c,0x6b,0x56,0x61,0x6c,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29};
//        char c102[]={0x03,0x00,0x04,0x04,0x02,0xf0,0x00,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x50,0x44,0x50,0x31,0x24,0x53,0x47,0x24,0x4f,0x70,0x44,0x6c,0x54,0x6d,0x6d,0x73,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x50,0x44,0x49,0x53,0x31,0x30,0x24,0x53,0x47,0x24,0x53,0x74,0x72,0x56,0x61,0x6c,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x44,0x49,0x53,0x31,0x24,0x53,0x47,0x24,0x4b,0x30,0x46,0x61,0x63,0x74,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2d,0xa0,0x2b,0xa1,0x29,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x1a,0x50,0x44,0x49,0x53,0x31,0x24,0x53,0x47,0x24,0x41,0x6e,0x67,0x4f,0x66,0x73,0x50,0x47,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2d,0xa0,0x2b,0xa1,0x29,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x1a,0x50,0x44,0x49,0x53,0x31,0x24,0x53,0x47,0x24,0x41,0x6e,0x67,0x4f,0x66,0x73,0x50,0x50,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2d,0xa0,0x2b,0xa1,0x29,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x1a,0x50,0x44,0x49,0x46,0x31,0x24,0x53,0x47,0x24,0x43,0x54,0x42,0x72,0x6b,0x56,0x61,0x6c,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2e,0xa0,0x2c,0xa1,0x2a,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x1b,0x52,0x50,0x53,0x42,0x31,0x24,0x53,0x47,0x24,0x42,0x6c,0x6b,0x56,0x61,0x6c,0x41,0x53,0x47,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x50,0x44,0x49,0x46,0x31,0x24,0x53,0x47,0x24,0x52,0x65,0x6d,0x53,0x68,0x52,0x58,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x44,0x49,0x46,0x31,0x24,0x53,0x47,0x24,0x52,0x65,0x6d,0x4e,0x52,0x58,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x29,0xa0,0x27,0xa1,0x25,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x16,0x50,0x44,0x49,0x46,0x31,0x24,0x53,0x47,0x24,0x45,0x6e,0x61,0x62,0x6c,0x65,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x44,0x49,0x46,0x31,0x24,0x53,0x47,0x24,0x43,0x54,0x42,0x6c,0x6b,0x45,0x6e,0x61,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x50,0x53,0x43,0x48,0x32,0x24,0x53,0x47,0x24,0x49,0x6e,0x74,0x43,0x6c,0x6b,0x4d,0x6f,0x64,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x54,0x56,0x54,0x52,0x31,0x24,0x53,0x47,0x24,0x4c,0x69,0x6e,0x54,0x56,0x4d,0x6f,0x64,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x29,0xa0,0x27,0xa1,0x25,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x16,0x52,0x50,0x53,0x42,0x31,0x24,0x53,0x47,0x24,0x45,0x6e,0x61,0x62,0x6c,0x65,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x29,0xa0,0x27,0xa1,0x25,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x16,0x50,0x44,0x49,0x53,0x31,0x24,0x53,0x47,0x24,0x45,0x6e,0x61,0x62,0x6c,0x65,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x29,0xa0,0x27,0xa1,0x25,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x16,0x50,0x44,0x49,0x53,0x34,0x24,0x53,0x47,0x24,0x45,0x6e,0x61,0x62,0x6c,0x65,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x29,0xa0,0x27,0xa1,0x25,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x16,0x50,0x44,0x49,0x53,0x37,0x24,0x53,0x47,0x24,0x45,0x6e,0x61,0x62,0x6c,0x65,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x29,0xa0,0x27,0xa1,0x25,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x16,0x50,0x54,0x4f,0x43,0x31,0x24,0x53,0x47,0x24,0x45,0x6e,0x61,0x62,0x6c,0x65,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x29,0xa0,0x27,0xa1,0x25,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x16,0x50,0x54,0x4f,0x43,0x32,0x24,0x53,0x47,0x24,0x44,0x69,0x72,0x45,0x6e,0x61,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x54,0x52,0x43,0x31,0x24,0x53,0x47,0x24,0x54,0x50,0x54,0x72,0x4d,0x6f,0x64,0x42,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x52,0x52,0x45,0x43,0x31,0x24,0x53,0x47,0x24,0x52,0x65,0x63,0x43,0x68,0x6b,0x53,0x79,0x6e,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x52,0x52,0x45,0x43,0x31,0x24,0x53,0x47,0x24,0x52,0x65,0x63,0x43,0x68,0x6b,0x44,0x65,0x61,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x54,0x52,0x43,0x31,0x24,0x53,0x47,0x24,0x5a,0x32,0x42,0x6c,0x6b};
//        char c103[]={0x03,0x00,0x02,0xb1,0x02,0xf0,0x80,0x52,0x65,0x63,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x50,0x54,0x52,0x43,0x31,0x24,0x53,0x47,0x24,0x4d,0x50,0x46,0x42,0x6c,0x6b,0x52,0x65,0x63,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x52,0x52,0x45,0x43,0x31,0x24,0x53,0x47,0x24,0x53,0x50,0x52,0x65,0x63,0x4d,0x6f,0x64,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x52,0x52,0x45,0x43,0x31,0x24,0x53,0x47,0x24,0x54,0x50,0x52,0x65,0x63,0x4d,0x6f,0x64,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x29,0xa0,0x27,0xa1,0x25,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x16,0x52,0x52,0x45,0x43,0x31,0x24,0x53,0x47,0x24,0x49,0x6e,0x68,0x52,0x65,0x63,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2a,0xa0,0x28,0xa1,0x26,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x17,0x52,0x52,0x45,0x43,0x31,0x24,0x53,0x47,0x24,0x53,0x74,0x6f,0x70,0x52,0x65,0x63,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2a,0xa0,0x28,0xa1,0x26,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x17,0x50,0x44,0x49,0x53,0x31,0x30,0x24,0x53,0x47,0x24,0x45,0x6e,0x61,0x62,0x6c,0x65,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x29,0xa0,0x27,0xa1,0x25,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x16,0x50,0x44,0x49,0x46,0x31,0x24,0x53,0x47,0x24,0x43,0x43,0x43,0x45,0x6e,0x61,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x29,0xa0,0x27,0xa1,0x25,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x16,0x50,0x50,0x44,0x50,0x31,0x24,0x53,0x47,0x24,0x45,0x6e,0x61,0x62,0x6c,0x65,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2a,0xa0,0x28,0xa1,0x26,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x17,0x50,0x50,0x44,0x50,0x31,0x24,0x53,0x47,0x24,0x41,0x42,0x6c,0x6b,0x45,0x6e,0x61,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x52,0x52,0x45,0x43,0x31,0x24,0x53,0x47,0x24,0x4f,0x70,0x6e,0x53,0x74,0x72,0x53,0x50,0x52,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x52,0x52,0x45,0x43,0x31,0x24,0x53,0x47,0x24,0x4f,0x70,0x6e,0x53,0x74,0x72,0x54,0x50,0x52,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x18,0x50,0x53,0x43,0x48,0x31,0x24,0x53,0x47,0x24,0x53,0x74,0x72,0x45,0x6e,0x61,0x52,0x54,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x50,0x44,0x49,0x53,0x31,0x24,0x53,0x47,0x24,0x52,0x69,0x73,0x4c,0x6f,0x64,0x45,0x6e,0x61,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x50,0x44,0x49,0x53,0x34,0x24,0x53,0x47,0x24,0x5a,0x32,0x53,0x4f,0x46,0x54,0x45,0x6e,0x61,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0b,0x50,0x4c,0x32,0x32,0x30,0x31,0x41,0x50,0x52,0x4f,0x54,0x1a,0x19,0x50,0x44,0x49,0x53,0x37,0x24,0x53,0x47,0x24,0x5a,0x33,0x53,0x4f,0x46,0x54,0x45,0x6e,0x61,0x24,0x73,0x65,0x74,0x56,0x61,0x6c};
//        
//        string strMsg;
//        strMsg.assign(c100,sizeof(c100));
//        Pack_TPKT vByte;
//        vByte.clear();
//        vByte.insert(vByte.end(),strMsg.begin(),strMsg.end());
//        
//        string strMsg1;
//        strMsg1.assign(c101,sizeof(c101));
//        Pack_TPKT vByte1;
//        vByte1.clear();
//        vByte1.insert(vByte1.end(),strMsg1.begin(),strMsg1.end());
//        
//        string strMsg2;
//        strMsg2.assign(c102,sizeof(c102));
//        Pack_TPKT vByte2;
//        vByte2.clear();
//        vByte2.insert(vByte2.end(),strMsg2.begin(),strMsg2.end());
//        
//        string strMsg3;
//        strMsg3.assign(c103,sizeof(c103));
//        Pack_TPKT vByte3;
//        vByte3.clear();
//        vByte3.insert(vByte3.end(),strMsg3.begin(),strMsg3.end());
//        
//        list_TPKT list_tpkt;
//        list_tpkt.push_back(vByte);
//        list_tpkt.push_back(vByte1);
//        list_tpkt.push_back(vByte2);
//        list_tpkt.push_back(vByte3);
//
//        CMMSAnalyze cAnalyze;
//        cAnalyze.Init(m_pLogFile);
//        MMS_Analyze sAnalyzeResult;
//        bool bok = cAnalyze.Analyze(list_tpkt,sAnalyzeResult);
//        if(bok){
//            printf("[CTaskMngr::Start()] strdomainID1[%s]\n",sAnalyzeResult.list_domian.at(0).DomainID.c_str());
//        }
//    }
//    printf("[CTaskMngr::Start()] ------------------\n");
//    {
//        char c100[]={0x03,0x00,0x00,0x4a,0x02,0xf0,0x80,0x01,0x00,0x01,0x00,0x61,0x3d,0x30,0x3b,0x02,0x01,0x03,0xa0,0x36,0xa0,0x34,0x02,0x02,0x2b,0xc7,0xa4,0x2e,0xa1,0x2c,0xa0,0x2a,0x30,0x28,0xa0,0x26,0xa1,0x24,0x1a,0x0a,0x50,0x4d,0x35,0x30,0x30,0x31,0x41,0x4c,0x44,0x30,0x1a,0x16,0x4c,0x4c,0x4e,0x30,0x24,0x42,0x52,0x24,0x62,0x72,0x63,0x62,0x52,0x65,0x6c,0x61,0x79,0x44,0x69,0x6e,0x30,0x36};
//        string strMsg;
//        strMsg.assign(c100,sizeof(c100));
//        Pack_TPKT vByte;
//        vByte.clear();
//        vByte.insert(vByte.end(),strMsg.begin(),strMsg.end());
//        list_TPKT list_tpkt;
//        list_tpkt.push_back(vByte);
//printf("[CTaskMngr::Start()] -------------2-----\n");
//        CMMSAnalyze cAnalyze;
//        cAnalyze.Init(m_pLogFile);
//        MMS_Analyze sAnalyzeResult;
//printf("[CTaskMngr::Start()] ---------3---------\n");
//        bool bok = cAnalyze.Analyze(list_tpkt,sAnalyzeResult);
//        if(bok){
//            printf("[CTaskMngr::Start()] strdomainID2[%s]\n",sAnalyzeResult.list_domian.at(0).DomainID.c_str());
//        }
//    }
//    

//  
//    {
//    CXJTime CvtTime( 0);
//    printf("[CTaskMngr::Start()] time 0[%s]\n",CvtTime.GetTimeString(CXJTime::STTP19Time).c_str());
//    }
//    {
//    CXJTime CvtTime( time(0));
//    printf("[CTaskMngr::Start()] time now[%s]\n",CvtTime.GetTimeString(CXJTime::STTP19Time).c_str());
//    }

//    while(!(*m_pExit)){
        ////多变量
//    char c100[]={0x03,0x00,0x03,0x6e,0x02,0xf0,0x80,0x01,0x00,0x01,0x00,0x61,0x82,0x03,0x5f,0x30,0x82,0x03,0x5b,0x02,0x01,0x03,0xa0,0x82,0x03,0x54,0xa0,0x82,0x03,0x50,0x02,0x02,0x01,0x38,0xa4,0x82,0x03,0x48,0xa1,0x82,0x03,0x44,0xa0,0x82,0x03,0x40,0x30,0x2a,0xa0,0x28,0xa1,0x26,0x1a,0x0c,0x43,0x45,0x4b,0x4f,0x41,0x41,0x54,0x45,0x4d,0x45,0x41,0x53,0x1a,0x16,0x52,0x53,0x59,0x4e,0x32,0x24,0x53,0x47,0x24,0x56,0x42,0x6c,0x6b,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2a,0xa0,0x28,0xa1,0x26,0x1a,0x0c,0x43,0x45,0x4b,0x4f,0x41,0x41,0x54,0x45,0x4d,0x45,0x41,0x53,0x1a,0x16,0x52,0x53,0x59,0x4e,0x32,0x24,0x53,0x47,0x24,0x44,0x69,0x66,0x56,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0c,0x43,0x45,0x4b,0x4f,0x41,0x41,0x54,0x45,0x4d,0x45,0x41,0x53,0x1a,0x17,0x52,0x53,0x59,0x4e,0x32,0x24,0x53,0x47,0x24,0x44,0x69,0x66,0x48,0x7a,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0c,0x43,0x45,0x4b,0x4f,0x41,0x41,0x54,0x45,0x4d,0x45,0x41,0x53,0x1a,0x17,0x52,0x53,0x59,0x4e,0x32,0x24,0x53,0x47,0x24,0x52,0x74,0x65,0x48,0x7a,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0c,0x43,0x45,0x4b,0x4f,0x41,0x41,0x54,0x45,0x4d,0x45,0x41,0x53,0x1a,0x18,0x52,0x53,0x59,0x4e,0x32,0x24,0x53,0x47,0x24,0x44,0x69,0x66,0x41,0x6e,0x67,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2d,0xa0,0x2b,0xa1,0x29,0x1a,0x0c,0x43,0x45,0x4b,0x4f,0x41,0x41,0x54,0x45,0x4d,0x45,0x41,0x53,0x1a,0x19,0x52,0x53,0x59,0x4e,0x32,0x24,0x53,0x47,0x24,0x4c,0x69,0x76,0x44,0x65,0x61,0x4d,0x6f,0x64,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x30,0xa0,0x2e,0xa1,0x2c,0x1a,0x0c,0x43,0x45,0x4b,0x4f,0x41,0x41,0x54,0x45,0x4d,0x45,0x41,0x53,0x1a,0x1c,0x52,0x53,0x59,0x4e,0x32,0x24,0x53,0x47,0x24,0x54,0x56,0x46,0x61,0x69,0x6c,0x42,0x6c,0x6b,0x44,0x65,0x61,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x30,0xa0,0x2e,0xa1,0x2c,0x1a,0x0c,0x43,0x45,0x4b,0x4f,0x41,0x41,0x54,0x45,0x4d,0x45,0x41,0x53,0x1a,0x1c,0x52,0x53,0x59,0x4e,0x32,0x24,0x53,0x47,0x24,0x54,0x56,0x46,0x61,0x69,0x6c,0x42,0x6c,0x6b,0x53,0x79,0x6e,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2f,0xa0,0x2d,0xa1,0x2b,0x1a,0x0c,0x43,0x45,0x4b,0x4f,0x41,0x41,0x54,0x45,0x4d,0x45,0x41,0x53,0x1a,0x1b,0x52,0x53,0x59,0x4e,0x32,0x24,0x53,0x47,0x24,0x53,0x79,0x6e,0x52,0x73,0x44,0x6c,0x54,0x6d,0x6d,0x73,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2d,0xa0,0x2b,0xa1,0x29,0x1a,0x0c,0x43,0x45,0x4b,0x4f,0x41,0x41,0x54,0x45,0x4d,0x45,0x41,0x53,0x1a,0x19,0x52,0x53,0x59,0x4e,0x32,0x24,0x53,0x47,0x24,0x43,0x68,0x6b,0x44,0x65,0x61,0x50,0x63,0x74,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2d,0xa0,0x2b,0xa1,0x29,0x1a,0x0c,0x43,0x45,0x4b,0x4f,0x41,0x41,0x54,0x45,0x4d,0x45,0x41,0x53,0x1a,0x19,0x52,0x53,0x59,0x4e,0x32,0x24,0x53,0x47,0x24,0x43,0x68,0x6b,0x4c,0x69,0x76,0x50,0x63,0x74,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0c,0x43,0x45,0x4b,0x4f,0x41,0x41,0x54,0x45,0x4d,0x45,0x41,0x53,0x1a,0x17,0x52,0x53,0x59,0x4e,0x32,0x24,0x53,0x47,0x24,0x53,0x79,0x6e,0x56,0x54,0x79,0x70,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2f,0xa0,0x2d,0xa1,0x2b,0x1a,0x0c,0x43,0x45,0x4b,0x4f,0x41,0x41,0x54,0x45,0x4d,0x45,0x41,0x53,0x1a,0x1b,0x52,0x53,0x59,0x4e,0x32,0x24,0x53,0x47,0x24,0x46,0x69,0x78,0x44,0x69,0x66,0x41,0x6e,0x67,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2b,0xa0,0x29,0xa1,0x27,0x1a,0x0c,0x43,0x45,0x4b,0x4f,0x41,0x41,0x54,0x45,0x4d,0x45,0x41,0x53,0x1a,0x17,0x52,0x53,0x59,0x4e,0x32,0x24,0x53,0x47,0x24,0x42,0x6b,0x72,0x54,0x6d,0x6d,0x73,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2a,0xa0,0x28,0xa1,0x26,0x1a,0x0c,0x43,0x45,0x4b,0x4f,0x41,0x41,0x54,0x45,0x43,0x54,0x52,0x4c,0x1a,0x16,0x41,0x54,0x43,0x43,0x31,0x24,0x53,0x47,0x24,0x54,0x61,0x70,0x4e,0x75,0x6d,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x28,0xa0,0x26,0xa1,0x24,0x1a,0x0c,0x43,0x45,0x4b,0x4f,0x41,0x41,0x54,0x45,0x43,0x54,0x52,0x4c,0x1a,0x14,0x41,0x54,0x43,0x43,0x31,0x24,0x53,0x47,0x24,0x43,0x6f,0x64,0x65,0x24,0x73,0x65,0x74,0x56,0x61,0x6c,0x30,0x2d,0xa0,0x2b,0xa1,0x29,0x1a,0x0c,0x43,0x45,0x4b,0x4f,0x41,0x41,0x54,0x45,0x43,0x54,0x52,0x4c,0x1a,0x19,0x41,0x54,0x43,0x43,0x31,0x24,0x53,0x47,0x24,0x44,0x65,0x62,0x54,0x6d,0x6d,0x73,0x24,0x73,0x65,0x74,0x4d,0x61,0x67,0x24,0x66,0x30,0x2c,0xa0,0x2a,0xa1,0x28,0x1a,0x0c,0x43,0x45,0x4b,0x4f,0x41,0x41,0x54,0x45,0x43,0x54,0x52,0x4c,0x1a,0x18,0x41,0x54,0x43,0x43,0x31,0x24,0x53,0x47,0x24,0x54,0x61,0x70,0x42,0x6c,0x6b,0x45,0x6e,0x24,0x73,0x65,0x74,0x56,0x61,0x6c};
////    char c100[]={0x03,0x00,0x00,0x54,0x02,0xf0,0x80,0x01,0x00,0x01,0x00,0x61,0x47,0x30,0x45,0x02,0x01,0x03,0xa0,0x40,0xa0,0x3e,0x02,0x02,0x01,0x9d,0xa5,0x38,0xa0,0x30,0x30,0x2e,0xa0,0x2c,0xa1,0x2a,0x1a,0x09,0x50,0x4c,0x31,0x31,0x30,0x39,0x4c,0x44,0x30,0x1a,0x1d,0x4c,0x4c,0x4e,0x30,0x24,0x42,0x52,0x24,0x62,0x72,0x63,0x62,0x52,0x65,0x6c,0x61,0x79,0x44,0x69,0x6e,0x30,0x36,0x24,0x54,0x72,0x67,0x4f,0x70,0x73,0xa0,0x04,0x84,0x02,0x02,0x44};
////    
//////        char c100[]={0x03,0x00,0x00,0x4f,0x02,0xf0,0x80,0x01,0x00,0x01,0x00,0x61,0x42,0x30,0x40,0x02,0x01,0x03,0xa0,0x3b,0xa0,0x39,0x02,0x02,0x01,0x89,0xa5,0x33,0xa0,0x2c,0x30,0x2a,0xa0,0x28,0xa1,0x26,0x1a,0x0a,0x50,0x4c,0x31,0x31,0x30,0x39,0x43,0x54,0x52,0x4c,0x1a,0x18,0x4c,0x4c,0x4e,0x30,0x24,0x42,0x52,0x24,0x62,0x72,0x63,0x62,0x44,0x69,0x6e,0x30,0x36,0x24,0x49,0x6e,0x74,0x67,0x50,0x64,0xa0,0x03,0x86,0x01,0x00};
////        //char c100[]={0xa0,0x39,0x02,0x02,0x01,0x89,0xa5,0x33,0xa0,0x2c,0x30,0x2a,0xa0,0x28,0xa1,0x26,0x1a,0x0a,0x50,0x4c,0x31,0x31,0x30,0x39,0x43,0x54,0x52,0x4c,0x1a,0x18,0x4c,0x4c,0x4e,0x30,0x24,0x42,0x52,0x24,0x62,0x72,0x63,0x62,0x44,0x69,0x6e,0x30,0x36,0x24,0x49,0x6e,0x74,0x67,0x50,0x64,0xa0,0x03,0x86,0x01,0x00};
//        string strMsg;
//        strMsg.assign(c100,sizeof(c100));
//        PK_FRAME sMMS2Analyze;
//        CMMSAnalyze cAnalyze;
//        cAnalyze.Init(m_pLogFile);
//        MMS_Analyze sAnalyzeResult;
//        bool bok = cAnalyze.Analyze(&sMMS2Analyze,sAnalyzeResult);
//        if(bok){
//            printf("[CTaskMngr::Start()] strdomainID[%s]\n",sAnalyzeResult.strdomainID.c_str());
//            
//        }
//    } 
//    return 0;


//    
//    char c100[]={0x03,0x00,0x00,0x5a,0x02,0xf0,0x80,0x01,0x00,0x01,0x00,0x61,0x4d,0x30,0x4b,0x02,0x01,0x03,0xa0,0x46,0xa0,0x44,0x02,0x01,0x50,0xbf,0x48,0x3e,0xa0,0x39,0x19,0x37,0x43,0x4f,0x4d,0x54,0x52,0x41,0x44,0x45,0x2f,0x50,0x4c,0x31,0x30,0x34,0x5f,0x52,0x43,0x44,0x5f,0x30,0x30,0x30,0x31,0x36,0x5f,0x32,0x30,0x32,0x33,0x30,0x34,0x31,0x38,0x5f,0x30,0x37,0x33,0x35,0x32,0x34,0x5f,0x33,0x32,0x37,0x5f,0x73,0x2e,0x63,0x66,0x67,0x50,0x4c,0x31,0x30,0x34,0x81,0x01,0x00};
//    string str2;
//    str2.assign(c100,sizeof(c100));
//    PrintBytes(str2);
//    
//    char Buff[65535]; 
//    string strMMS = str2.substr(20);
//    memcpy(Buff, strMMS.data(), strMMS.length());
//    PrintBytes(strMMS);
//    PrintBytes(Buff,strMMS.length());
//    printf("tpkt[%d]mms[%d]\n",str2.length(),strMMS.length());


//    string strMMS = "a024020109a41fa11da11ba1191a09504231303950524f541a0c4c4c4e30246473416c61726d";
//    char c[2];
//    unsigned char bytes[2];
//    unsigned char vBuff[65535];
//    int cont=0;
//    for(int j=0; j<strMMS.length();j+=2){
//        if(0==j%2){
//            c[0]=strMMS[j];
//            c[1]=strMMS[j+1];
//            
//            sscanf(c,"%02X",&bytes[0]);
//            vBuff[cont]=bytes[0];
//            printf("%02X ",vBuff[cont]);
//            cont++;
//        }
//    }
//    int  len = cont;
//    printf("\n");
//    for(int i=0;i<len;i++){
//        printf("%02X ",vBuff[i]);
//    }
//    printf("\n");
//    char c[]="中元1BH999999";
//    
//    if(strstr(c,"BH99999")){
//        string str1 = c; 
//        std::size_t found = str1.find("BH99999");
//        if (found!=std::string::npos){
//            string strStn = str1.substr(0,found);
//            printf("------------------strStn[%s]-------------------\n",strStn.c_str());
//        }
//    }

//    //3c 84 00 00 13 04 17
//    char ctime[]={0x3c,0x84,0x00,0x00,0x13,0x04,0x17,0x00};
//    string strctime;strctime.assign(ctime,sizeof(ctime));
//    CXJTime pTime;
//    pTime.AssignTimeString(strctime,CXJTime::CP56Time2a);
//    string strTime = pTime.GetTimeString(CXJTime::STTP15Time);//“YYMMDDmmhhss+3位毫秒数”
//    PrintBytes(ctime,8);
//    printf("---time[%s]----\n",strTime.c_str());
//    //19 6e 02 01 13 04 17 00
//    char ctime2[]={0x19,0x6e,0x02,0x01,0x13,0x04,0x17,0x00};
//    string strctime2;strctime2.assign(ctime2,sizeof(ctime2));
//    CXJTime pTime2;
//    pTime2.AssignTimeString(strctime2,CXJTime::CP56Time2a);
//    string strTime2 = pTime2.GetTimeString(CXJTime::STTP15Time);//“YYMMDDmmhhss+3位毫秒数”
//    PrintBytes(ctime2,8);
//    printf("---time2[%s]----\n",strTime2.c_str());

//    char c1[]={0x03,0x00,0x00,0x07};
//    string str1;str1.assign(c1,sizeof(c1));
//    char c2[]={0x03,0x00,0x00,0x01,0x00};
//    string str2;str2.assign(c2,sizeof(c2));
//    char c3[]={0x07,0x08,0x09};
//    string str3;str3.assign(c3,sizeof(c3));
//    
//    vector<BYTE> v1test;
//    v1test.assign(str1.begin(),str1.end());
//    PrintBytes(v1test);//03 00 00 03 
//    v1test.erase(v1test.begin(),v1test.begin()+2);//发完之后删2哥
//    PrintBytes(v1test);//-- -- 00 03 
//    
//    vector<BYTE> vMsg;
//    vMsg.assign(str1.begin(),str1.end());
//    PrintBytes(vMsg);//03 00 00 03
//    vMsg.insert(vMsg.end(),str3.begin(),str3.end());
//    PrintBytes(vMsg);//03 00 00 03 07 08 09 
//    


//    
//    char c100[]={0x03,0x00,0x00,0x2f,0x02,0xf0,0x80,0x01,0x00,0x01,0x00,0x61,0x22,0x30,0x20,0x02,0x01,0x03,0xa0,0x1b,
//0xa1,
//0x19,
//0x01,
//0x02,
//0x9f,0x49,
//0x13,0xa0,0x11,0x19,0x0f,0x43,0x4f,0x4d,0x54,0x52,0x41,0x44,0x45,0x2f,0x3f,0x50,0x4c,0x31,0x30,0x34};
//    //char c100[]={0x03,0x00,0x00,0x55,0x02,0xf0,0x80,0x01,0x00,0x01,0x00,0x61,0x48,0x30,0x46,0x02,0x01,0x03,0xa0,0x41,0xa0,0x3f,0x02,0x01,0x47,0xbf,0x48,0x39,0xa0,0x34,0x19,0x32,0x43,0x4f,0x4d,0x54,0x52,0x41,0x44,0x45,0x2f,0x50,0x4c,0x31,0x30,0x34,0x5f,0x52,0x43,0x44,0x5f,0x30,0x30,0x30,0x32,0x32,0x5f,0x32,0x30,0x32,0x33,0x30,0x34,0x32,0x30,0x5f,0x30,0x30,0x32,0x33,0x33,0x34,0x5f,0x31,0x37,0x38,0x5f,0x73,0x2e,0x63,0x66,0x67,0x81,0x01,0x00};
//    //char c100[]={0x03,0x00,0x00,0x2f,0x02,0xf0,0x80,0x01,0x00,0x01,0x00,0x61,0x22,0x30,0x20,0x02,0x01,0x03,0xa0,0x1b,0xa0,0x19,0x02,0x01,0x34,0xbf,0x4d,0x13,0xa0,0x11,0x19,0x0f,0x2f,0x43,0x4f,0x4d,0x54,0x52,0x41,0x44,0x45,0x2f,0x50,0x4c,0x31,0x30,0x34};
//    //char c100[]={0x03,0x00,0x00,0x5a,0x02,0xf0,0x80,0x01,0x00,0x01,0x00,0x61,0x4d,0x30,0x4b,0x02,0x01,0x03,0xa0,0x46,0xa0,0x44,0x02,0x01,0x50,0xbf,0x48,0x3e,0xa0,0x39,0x19,0x37,0x43,0x4f,0x4d,0x54,0x52,0x41,0x44,0x45,0x2f,0x50,0x4c,0x31,0x30,0x34,0x5f,0x52,0x43,0x44,0x5f,0x30,0x30,0x30,0x31,0x36,0x5f,0x32,0x30,0x32,0x33,0x30,0x34,0x31,0x38,0x5f,0x30,0x37,0x33,0x35,0x32,0x34,0x5f,0x33,0x32,0x37,0x5f,0x73,0x2e,0x63,0x66,0x67,0x50,0x4c,0x31,0x30,0x34,0x81,0x01,0x00};
//    string str2;
//    str2.assign(c100,sizeof(c100));
//    PrintBytes(str2);
//    int nIsFile = DecodeMMsType(str2);
//    printf("------------nIsFile [%d] \n",nIsFile);
//    
//    

//    string strIedName="PL104";//50 4c 31 30 34
//    PrintBytes(strIedName);
//    int nPos;
//    int ret = isSubarray(strIedName,str2,nPos);
//    m_pLogFile->FormatAdd(CLogFile::trace,"------isSubarray strIedName[%s] ret[%d]pos[%d]",strIedName.c_str(),ret,nPos);
//    if(ret ==3){
//        string strCom="COMTRADE";
//        int nPos2;
//        ret = isSubarray(strCom,str2,nPos2);
//       m_pLogFile->FormatAdd(CLogFile::trace,"------isSubarray strCom[%s]ret[%d]pos[%d]",strCom.c_str(),ret,nPos2);
//        if(ret ==3){
//    //        str2.swap(strIedName);
//    //       m_pLogFile->FormatAdd(CLogFile::trace,"------isSubarray swap str2[%s]",str2.c_str());
//            string strKeyFind;
//            strKeyFind = "?" + strIedName;//3F 50 4C 31 30 34
//            //int nfind = str2.rfind(strKeyFind);
//            //m_pLogFile->FormatAdd(CLogFile::trace,"------isSubarray rfind n [%d]",nfind);
//            PrintBytes(strKeyFind);
//            
//            ret = isSubarray(strKeyFind,str2,nPos2);
//            if(ret ==3){
//                m_pLogFile->FormatAdd(CLogFile::trace,"------isSubarray  nPos2 [%d]",nPos2);
//                for(int i=0;i<strKeyFind.size();i++){
//                    char c[]={0x00};
//                    string strNull;
//                    strNull.assign(c,sizeof(c));
//                    str2.replace(nPos2+i,1,strNull);
//                    PrintBytes(str2);
//                }
//            }
//        }
//    }

//    

//        char buffer[8]={0xB0,0x36,0x32,0x0B,0x59,0x04,0x17};
//		CXJTime pTime;
//		pTime.AssignTimeString(buffer,CXJTime::CP56Time2a);
//		string  strData = pTime.GetTimeString(CXJTime::STTP23Time);
//       m_pLogFile->FormatAdd(CLogFile::trace,"----------[%s]---------",strData.c_str());

//    vector<BYTE> vBytes;//
//    vBytes.insert(vBytes.end(),1,1);
//    vBytes.insert(vBytes.end(),1,2);
//    vBytes.insert(vBytes.end(),1,3);
//    vBytes.insert(vBytes.end(),1,4);
//    uint32 uAll;
//    uAll = vBytes.at(0)+vBytes.at(1)*0x100+vBytes.at(2)*0x10000+vBytes.at(3)*0x1000000;
//   m_pLogFile->FormatAdd(CLogFile::trace,"------[%d]-----",uAll);
//    
//    ReverseIntEndian;
//    time_t t1,t2;
//    t1=1681890880;
//    t2=1681890880;
//    UINT l=30;
//    if ((t1 - t2) > l*0.1){
//       m_pLogFile->FormatAdd(CLogFile::trace,"--------[%d][%d] > [%f]--------",t1,t2,l*0.1);
//    }
//    if ((t1 - t2) > (l*0.1)){
//       m_pLogFile->FormatAdd(CLogFile::trace,"--------[%d][%d] > ()[%f]--------",t1,t2,l*0.1);
//    }


//    string str1="/home1/zx/var/zexin/zxdown/泽鑫/通用文件/CONFIG/nrz.scd";
//    string strHome="/home/<USER>/var/zexin/zxdown";
//    int nPos = str1.find(strHome);
//    if(nPos==0){
//        string strCut = str1.substr( strHome.length(),str1.length()-strHome.length() );
//       m_pLogFile->FormatAdd(CLogFile::trace,"------[%s]----------",strCut.c_str());
//    } else{
//       m_pLogFile->FormatAdd(CLogFile::trace,"------ find- not begin--[%d]--",nPos);
//    }

//    vector<BYTE> vBytes;//0x0201
//    vBytes.insert(vBytes.end(),1,1);
//    vBytes.insert(vBytes.end(),1,2);
//    
//    vector<BYTE>::iterator itCur=vBytes.begin();
//    string str1(itCur,itCur+1);itCur++;
//    string str2(itCur,itCur+1);itCur++;
//    string str3;str3.assign(str2);

//    string strCC;
//    strCC.assign(CC,sizeof(CC));
//   m_pLogFile->FormatAdd(CLogFile::trace,"-----size[%d]char[%02x]",strCC.length(),(unsigned char)strCC.at(5));
//    

//    vector<BYTE> vBytes;
//    vBytes.insert(vBytes.end(),1,1);
//    vBytes.insert(vBytes.end(),1,0);
//    vBytes.insert(vBytes.end(),1,3);
//    vBytes.insert(vBytes.end(),1,4);
//    vector<BYTE>::iterator itCur=vBytes.begin();
//    
//    string str1(itCur,itCur+3);itCur++;itCur++;itCur++;
//    unsigned char buffer1[str1.length()];
//    memcpy(buffer1, str1.data(), str1.length());
//    for(int i=0;i<str1.length();i++){
//       m_pLogFile->FormatAdd(CLogFile::trace,"0x[%02x]",buffer1[i]);
//    }
//   m_pLogFile->FormatAdd(CLogFile::trace,"");
//    
//    string strId="987";
//    strId.assign(str1);//
//        unsigned char buffer[strId.length()];
//    memcpy(buffer, strId.data(), strId.length());
//    for(int i=0;i<strId.length();i++){
//       m_pLogFile->FormatAdd(CLogFile::trace,"0x[%02x]",buffer[i]);
//    }
//   m_pLogFile->FormatAdd(CLogFile::trace,"");
//    

//    vector<BYTE> vBytes;
//    vBytes.resize(14);
//   m_pLogFile->FormatAdd(CLogFile::trace,"14size[%d]",vBytes.size());
//    vBytes.resize(5);
//    m_pLogFile->FormatAdd(CLogFile::trace,"5size[%d]",vBytes.size());

//    string str1,str2;
//    str1 = "138600LD01234";
//    str1[1]=0;
//    str2 = "138600LD012";
//    str2[1]=0;//str2[2]=0;
//   m_pLogFile->FormatAdd(CLogFile::trace,"[%s][%s]",str1.c_str(),str2.c_str());
//    if(strstr(str1.c_str(),str2.c_str())){
//       m_pLogFile->FormatAdd(CLogFile::trace,"has ");
//    }
//    string str3=str2;//等号不按\0截取，copy的
//   m_pLogFile->FormatAdd(CLogFile::trace,"[%d][%d][%d]",str1.length(),str2.length(),str3.length());
//    isSubarray(str1,str2);


//    char c100[100]="123456789";
//    string str2;
//    str2.assign(c100,3);
//   m_pLogFile->FormatAdd(CLogFile::trace,"[%s]",str2.c_str());

//    CXJTime time56;
//    string strtime="2023-02-01 12:13:14.123456";//yyyy-mm-dd HH:MM:SS.6位微妙
//    time56.AssignTimeString(strtime,CXJTime::STTP26Time);
//   m_pLogFile->FormatAdd(CLogFile::trace,"[%s][%s]",strtime.c_str(),time56.GetTimeString(CXJTime::CP56Time2a).c_str());
//    
//        in_addr_t add = inet_addr("127.1.2.3");//network byte order L,L,H,H
//        uint8 u1,u2,u3,u4;
//        u4 = (uint8)(add & 0xff);//7f
//        u3 = (uint8)(add>>8 & 0xff);//01
//        u2 = (uint8)(add>>16 & 0xff);//02
//        u1 = (uint8)(add>>24 & 0xff);//03
//       m_pLogFile->FormatAdd(CLogFile::trace,"[%02x][%02x][%02x][%02x]",u1,u2,u3,u4);


//    vector<BYTE> vBytes;
//    string str="123456";
//    for ( std::string::iterator it=str.begin(); it!=str.end(); ++it){
//        vBytes.insert(vBytes.end(),1,*it);
//    }
//   m_pLogFile->FormatAdd(CLogFile::trace,"BYTEs");
//    for(vector<BYTE>::iterator it=vBytes.begin();it!=vBytes.end();it++){
//       m_pLogFile->FormatAdd(CLogFile::trace,"0x[%02x]",*it);
//    }
//   m_pLogFile->FormatAdd(CLogFile::trace,"");
//    string str1(vBytes.begin()+1,vBytes.begin()+3);
//   m_pLogFile->FormatAdd(CLogFile::trace,"[%s]",str1.c_str());
//    string str2(vBytes.begin()+3,vBytes.begin()+4);
//   m_pLogFile->FormatAdd(CLogFile::trace,"[%s]",str2.c_str());
//    set<string> setItems;
//    setItems.insert("10");
//    setItems.insert("1024");
//    setItems.insert("13");
//    unsigned int uNumItem = setItems.size()
//    unsigned int uLength = uNumItem*2+2;
//   m_pLogFile->FormatAdd(CLogFile::trace,"uNumItem[%d]uLength[%d]",uNumItem,uLength);
//    vector<BYTE> vBytes;
//    //vBytes.resize(uLength);
//    int nGroup =0;
//    vBytes.insert(vBytes.end(),1,nGroup);
//    vBytes.insert(vBytes.end(),1,uNumItem);
//    for(set<string>::iterator it=setItems.begin();it!=setItems.end();it++){
//        uint16 uVal = atoi(it->c_str());
//        uint8 uH,uL;
//        uL = (uint8)(uVal & 0xff);
//        uH = (uint8)(uVal>>8 & 0xff);
//       m_pLogFile->FormatAdd(CLogFile::trace,"uVal[%d] = uL[%d]0x[%02x] + uH[%d]0x[%02x]",uVal,uL,uL,uH,uH);
//        vBytes.insert(vBytes.end(),1,uL);
//        vBytes.insert(vBytes.end(),1,uH);
//    }
//   m_pLogFile->FormatAdd(CLogFile::trace,"vBytesSize[%d]end[%d]",vBytes.size(),*vBytes.end());
//   m_pLogFile->FormatAdd(CLogFile::trace,"BYTEs");
//    for(vector<BYTE>::iterator it=vBytes.begin();it!=vBytes.end();it++){
//       m_pLogFile->FormatAdd(CLogFile::trace,"0x[%02x]",*it);
//    }
//   m_pLogFile->FormatAdd(CLogFile::trace,"");
//    string str1(vBytes.begin(),vBytes.end());
//    m_pLogFile->FormatAdd(CLogFile::trace,"str1[%s]",str1.c_str());
//    unsigned char buffer[str1.length()];
//    memcpy(buffer, str1.data(), str1.length());
//   m_pLogFile->FormatAdd(CLogFile::trace,"str1");
//    for(int i=0;i<str1.length();i++){
//       m_pLogFile->FormatAdd(CLogFile::trace,"0x[%02x]",buffer[i]);
//    }
//   m_pLogFile->FormatAdd(CLogFile::trace,"");



//    char buf[]={0x03,0x00,0x00,0xc2};
//    int i=0;
//    for(i=0;i<sizeof(buf);i++){
//       m_pLogFile->FormatAdd(CLogFile::trace,",0x%02x",(unsigned char)buf[i]);
//    }
//   m_pLogFile->FormatAdd(CLogFile::trace,"");
//   m_pLogFile->FormatAdd(CLogFile::trace,"buf[2]=0x[%02x] [%02x]",buf[2],(unsigned char)buf[2]);
//   m_pLogFile->FormatAdd(CLogFile::trace,"buf[3]=0x[%02x] [%02x]",buf[3],(unsigned char)buf[3]);
//   m_pLogFile->FormatAdd(CLogFile::trace,"buf[2]<<8 = 0x[%02x]",( ( (unsigned short) buf[2] ) << 8 ));
//   m_pLogFile->FormatAdd(CLogFile::trace,"buf[2]<<8|buf[3] = 0x[%02x]",( ((unsigned short)buf[2]) << 8 )|(unsigned char)buf[3]);
//
//    

//    int tpkt_len=0;
//    tpkt_len = ( ( (unsigned short) vMsg.at(2) ) << 8 ) | (unsigned char)vMsg.at(3);
//    printf("-----tpkt len [%d] msg size[%d]----\n",tpkt_len,vMsg.size());
//    char buf[BUF_SZ];bzero(buf,BUF_SZ);
//    memcpy(buf, vMsg.data(), tpkt_len);
//    PrintBytes(buf,tpkt_len);
//    
    Pre102();
    Pre103();

    regisAsdu200Tc103(); // lmy add 
    
    int nRet=0;	
    nRet = xj_thread_create(&m_hTheadHandeTaskMngr,&m_hTheadIdTaskMngr,_TaskMngrThread,this);
    if( nRet != 0 ){
        m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Start()] _TaskMngrThread 线程 启动时失败 原因:%s(%d)。",strerror(errno),errno);
        return false;
    }else{
        m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::Start()] _TaskMngrThread 线程ID[%ld] 启动成功。",m_hTheadIdTaskMngr);
    }
	return true;	
}
bool CTaskMngr::Free104StnRes(STN_APCI &sStn)
{
    if(sStn.pAPCIHander !=NULL ){
        delete sStn.pAPCIHander;
        sStn.pAPCIHander = NULL;
    }
    if(sStn.pMsgCaster !=NULL ){
        delete sStn.pMsgCaster;
        sStn.pMsgCaster = NULL;
    }
    if(sStn.pMsg104AttachFactory !=NULL ){
        delete sStn.pMsg104AttachFactory;
        sStn.pMsg104AttachFactory = NULL;
    }
    if(sStn.pFlowLog !=NULL ){
        delete sStn.pFlowLog;
        sStn.pFlowLog = NULL;
    }
    if(sStn.pMessageLog !=NULL ){
        delete sStn.pMessageLog;
        sStn.pMessageLog = NULL;
    }
    if(sStn.pFlowASDUHander !=NULL ){
        delete sStn.pFlowASDUHander;
        sStn.pFlowASDUHander = NULL;
    }
    if(sStn.pStnFlow !=NULL ){
        delete sStn.pStnFlow;
        sStn.pStnFlow = NULL;
    }
    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::Free104StnRes()] finish");
	return true;	
}
bool CTaskMngr::End()
{
    if(m_hTheadIdTaskMngr != 0){
		int nRet = xj_thread_join(m_hTheadIdTaskMngr,NULL);
		if(nRet != 0){
			m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::End()]退出_TaskMngrThread线程异常，原因为:%s",strerror(errno));
		}
        m_hTheadIdTaskMngr = 0;
	}
    for(map<string,map<string,pthread_t > >::iterator it=map102PushTheadId.begin();it!=map102PushTheadId.end();it++){
        for(map<string,pthread_t >::iterator itTh=it->second.begin();itTh!=it->second.end();itTh++){
            pthread_t ThreadId = itTh->second;
            if(ThreadId != 0){
                int nRet = pthread_join(ThreadId,NULL);
                if(nRet != 0){
                    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::End()]102退出[%s]线程ThreadId[%ld]异常ret[%d]，原因为:%s",
                            it->first.c_str(),itTh->first.c_str(),ThreadId,nRet,strerror(errno));
                }else{
                   m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::End()]102退出[%s]线程ThreadId[%ld]成功ret[%d]",
                            it->first.c_str(),itTh->first.c_str(),ThreadId,nRet); 
                }
            }
        }
    }
    for(map<string,STN_CFG>::iterator itStnLink=mapStnLinkCfg.begin();itStnLink!=mapStnLinkCfg.end();itStnLink++){
        if(itStnLink->second.sApciPoiner.pStnFlow!=NULL){
            itStnLink->second.sApciPoiner.pStnFlow->ReleaseResource();
        }
    }

// lmy add 
    for(map<string,map<string,pthread_t > >::iterator it=map103PushTheadId.begin();it!=map103PushTheadId.end();it++)
    {
        for(map<string,pthread_t >::iterator itTh=it->second.begin();itTh!=it->second.end();itTh++)
        {
            pthread_t ThreadId = itTh->second;
            if(ThreadId != 0){
                int nRet = pthread_join(ThreadId,NULL);
                if(nRet != 0){
                    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::End()]103退出[%s]线程ThreadId[%ld]异常ret[%d]，原因为:%s",
                            it->first.c_str(),itTh->first.c_str(),ThreadId,nRet,strerror(errno));
                }else{
                   m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::End()]103退出[%s]线程ThreadId[%ld]成功ret[%d]",
                            it->first.c_str(),itTh->first.c_str(),ThreadId,nRet); 
                }
            }
        }
    }
    push103 *pPush = NULL;
    for(map<string,push103* >::iterator it=map103Push.begin();it!=map103Push.end();it++)
    {
	pPush = it->second;
	if(NULL != pPush)
	{
		delete pPush;
		pPush = NULL;
	}
    }
    
    SecDevFlowMoudle::getInstanse()->release();
    
    // lmy end

    // 停止并清理服务器在线管理器
    if (m_pSrvOnlineMngr != NULL) {
        char errorMsg[256] = "";
        m_pSrvOnlineMngr->StopSrvOnLineManager(errorMsg);
        delete m_pSrvOnlineMngr;
        m_pSrvOnlineMngr = NULL;
        m_pLogFile->Add("[CTaskMngr::End()] 服务器在线管理器已停止", CLogFile::trace);
    }

    UnInitCvtLib();
    if(sCtlKeyCfg.nUseDb==1){
        if(m_pDBAcess!=NULL){
            delete m_pDBAcess;
        }
    }
    if(NULL!=m_pLoadBusSwapLib) {
        m_pIZxBusSwap->StopBusSwap();
        m_pLoadBusSwapLib->DestroyBusIns(m_pIZxBusSwap);
        delete m_pLoadBusSwapLib;
    }
    
    if(m_pLogFile!=NULL){
        delete m_pLogFile;
    }
	return true;	
}

bool CTaskMngr::Init_daemon()
{
    //读取ini文件
    if(LoadConfigFile()!=0){
        printf("[CTaskMngr::Init_daemon()] LoadConfigFile()配置失败。\n");
		return false;
    }
        
	//初始化日志类。
	if (!InitLogFile()){
		printf("[CTaskMngr::Init_daemon()] 初始化日志配置失败。");
		return false;
	}
    
    
    //用db获取信息
    if(sCtlKeyCfg.nUseDb==1){
        //加载数据库接口。
        m_pDBAcess = new CXJDBFacade(m_pLogFile);
        if ( NULL == m_pDBAcess){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Init_daemon()] 实例化数据库接口类失败。");
            return false;
        }
        if ( 0 != m_pDBAcess->LoadLib()){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Init_daemon()] 实例化数据库接口类LoadLib失败。");
            return false;
        }
        //设置数据库链接数。
        if (!m_pDBAcess->SetConnNum(1)){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Init_daemon()] 设置数据库连接数失败。");
            return false;
        }
        char cErr[255]="";
        if (!m_pDBAcess->Connect(STTPNET_MODEL_MODEL_CHECK,cErr)){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Init_daemon()] 连接数据库失败(%s)。",cErr);
            return false;
        }
        char cTmpPath[500] = "";
        memcpy(cTmpPath,m_LogCfg.strLogPath.c_str(),m_LogCfg.strLogPath.length());
        memset(cErr,0,255);
        if ( !m_pDBAcess->SetLogPath(cTmpPath,cErr)){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Init_daemon()] 设置数据库接口的日志路径为%s时失败(%s)。", cTmpPath, cErr);
            return false;
        }
        //获取数据库 日志参数
        if (!GetLogSetFromDB()) {
            m_pLogFile->Add("[CTaskMngr::Init()] 读取TB_COMMU_LOCAL_CONFIG日志参数失败!! 用配置文件日志参数",CLogFile::error);	
        }else{
            if (!InitLogFileFromDb()){
                m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Init_daemon()] 按数据库参数 初始化日志配置 失败。");
                return false; 
            }else{
                char cTmpPath[500] = "";
                memcpy(cTmpPath, m_LogCfg.strLogPath.c_str(), m_LogCfg.strLogPath.length());
                memset(cErr,0,255);
                if ( !m_pDBAcess->SetLogPath(cTmpPath, cErr)){
                    m_pLogFile->FormatAdd(CLogFile::error, "[CTaskMngr::Init_daemon()] 设置数据库接口  的日志路径  为%s时失败(%s)。", cTmpPath, cErr);
                    return false;
                }
            }
        }
    }
    
	return true;
}

bool CTaskMngr::Init()
{
    //读取ini文件
    if(LoadConfigFile()!=0){
        printf("[CTaskMngr::Init()] LoadConfigFile()配置失败。\n");
		return false;
    }
        
	//初始化日志类。
	if (!InitLogFile()){
		printf("[CTaskMngr::Init()] 初始化日志配置失败。");
		return false;
	}
    
    
    //用db获取信息
    if(sCtlKeyCfg.nUseDb==1){
        //加载数据库接口。
        m_pDBAcess = new CXJDBFacade(m_pLogFile);
        if ( NULL == m_pDBAcess){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Init()] 实例化数据库接口类失败。");
            return false;
        }
        if ( 0 != m_pDBAcess->LoadLib()){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Init()] 实例化数据库接口类LoadLib失败。");
            return false;
        }
        //设置数据库链接数。
        if (!m_pDBAcess->SetConnNum(1)){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Init()] 设置数据库连接数失败。");
            return false;
        }
        char cErr[255]="";
        if (!m_pDBAcess->Connect(STTPNET_MODEL_MODEL_CHECK,cErr)){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Init()] 连接数据库失败(%s)。",cErr);
            return false;
        }
        char cTmpPath[500] = "";
        memcpy(cTmpPath,m_LogCfg.strLogPath.c_str(),m_LogCfg.strLogPath.length());
        memset(cErr,0,255);
        if ( !m_pDBAcess->SetLogPath(cTmpPath,cErr)){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Init()] 设置数据库接口的日志路径为%s时失败(%s)。", cTmpPath, cErr);
            return false;
        }
        //获取数据库 日志参数
        if (!GetLogSetFromDB()) {
            m_pLogFile->Add("[CTaskMngr::Init()] 读取TB_COMMU_LOCAL_CONFIG日志参数失败!! 用配置文件日志参数",CLogFile::error);	
        }else{
            if (!InitLogFileFromDb()){
                m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Init()] 按数据库参数 初始化日志配置 失败。");
                return false; 
            }else{
                char cTmpPath[500] = "";
                memcpy(cTmpPath, m_LogCfg.strLogPath.c_str(), m_LogCfg.strLogPath.length());
                memset(cErr,0,255);
                if ( !m_pDBAcess->SetLogPath(cTmpPath, cErr)){
                    m_pLogFile->FormatAdd(CLogFile::error, "[CTaskMngr::Init()] 设置数据库接口  的日志路径  为%s时失败(%s)。", cTmpPath, cErr);
                    return false;
                }
            }
        }
        
        if(!m_StnRun.empty()){
            //获取管理单元PTID+StnId，因为出现了BH99999后长度超了的问题
            if(0!=GetStnMngrPtIdFromDB()){
                m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Init()] GetStnMngrPtIdFromDB() 数据库获取子站管理单元ptid失败");
                return false;
            }
        }

		//读取厂站通信状态上送周期.
		m_nCylSstnStatus = __ReadSstnStatusAutoCyl();
    }
    
    
    
    //初始化消息中心客户端（加载消息中心客户端动态库，注册主题和回调函数）
	if ( 0 != init_msg_center_client()){
		m_pLogFile->Add("[CTaskMngr::Init()] 初始化消息中心客户端失败。",CLogFile::error);
		return false;
	}
    
    //加载字符转换动态库
    if ( false == InitCvtLib()) {
		m_pLogFile->Add("[CTaskMngr::Init()] 初始化 字符集编码转换接口库 失败。",CLogFile::error);
		return false;
	}

    // 初始化服务器在线管理器
    if (InitSrvOnlineManager() != 0) {
        m_pLogFile->Add("[CTaskMngr::Init()] 初始化服务器在线管理器失败，跳过初始化", CLogFile::warning);
        // 注意：这里不返回false，因为服务器在线管理器是可选功能
    }

	_SttpTypeCatch();

	return true;
}

int CTaskMngr::PreAPCI()
{
    for(map<string,STN_CFG>::iterator itStnLink=mapStnLinkCfg.begin();itStnLink!=mapStnLinkCfg.end();itStnLink++){
        string strStnMgrPtID;
        map<string,string>::iterator itb = m_mapStnMgrPt.find(itStnLink->first);
        if(itb == m_mapStnMgrPt.end()){
            strStnMgrPtID = itStnLink->first + "BH99999";
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::PreAPCI]stn[%s]无管理单元ptid,用[%s]作管理单元ptid，初始化此厂站业务流",itStnLink->first.c_str(),strStnMgrPtID.c_str());
        }else{
            strStnMgrPtID = itb->second;
        }
        
        CXJGB103MsgAttachFactory *pMsg104AttachFactory = new CXJGB103MsgAttachFactory;
        if(pMsg104AttachFactory == NULL){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Start()] stn[%s] new CXJ104Len2CpuMsgAttach 失败",itStnLink->first.c_str());
            continue;
        }else{
            itStnLink->second.sApciPoiner.pMsg104AttachFactory = pMsg104AttachFactory;
        }
        {
            bzero(&itStnLink->second.sApciPoiner.Interface,sizeof(CLIENT_PRO_INTERFACE));
            itStnLink->second.sApciPoiner.Interface.iNO = itStnLink->second.nNo;
            itStnLink->second.sApciPoiner.Interface.p_bExit = m_pExit;
            itStnLink->second.sApciPoiner.Interface.pszStationId = itStnLink->second.cStnId;
            itStnLink->second.sApciPoiner.Interface.pszClientName =  itStnLink->second.cDragonPath;
                STRUCT_DESTINATION_SERVER* pDestination = new STRUCT_DESTINATION_SERVER;
                pDestination->nWorkPart = 1;
                pDestination->bHasReservedServer = false;//无B备机
                pDestination->serverB.bHasReservedAddr = false;//B备机无备地址
                
                
                snprintf(pDestination->serverA.netAddrA.IpAddr,sizeof(pDestination->serverA.netAddrA.IpAddr),"%s",itStnLink->second.strStnIP.c_str());
                pDestination->serverA.netAddrA.nPort = itStnLink->second.nStnPort;
                if(itStnLink->second.strStnChnBIP.empty()) {
                    pDestination->serverA.bHasReservedAddr = false;
                }else{
                    pDestination->serverA.bHasReservedAddr = true;
                    snprintf(pDestination->serverA.netAddrB.IpAddr,sizeof(pDestination->serverA.netAddrB.IpAddr),"%s",itStnLink->second.strStnChnBIP.c_str());
                    pDestination->serverA.netAddrB.nPort = itStnLink->second.nStnPort;
                }
                
            itStnLink->second.sApciPoiner.Interface.pDestination = pDestination;
            sprintf(itStnLink->second.sApciPoiner.Interface.log_path,"%s",m_LogCfg.strLogPath.c_str());
            itStnLink->second.sApciPoiner.Interface.log_level = m_LogCfg.nLogLevel;
            itStnLink->second.sApciPoiner.Interface.ilog_day = m_LogCfg.nLogRrdDay;
            itStnLink->second.sApciPoiner.Interface.bRecordMsg = m_LogCfg.bShowDebug;
            itStnLink->second.sApciPoiner.Interface.iRunType = 1;
            //itStnLink->second.sApciPoiner.Interface.nAutoUpCylSstnStatus = CYCLETIME;//判断断开的时间s,周期召唤的时间间隔
            itStnLink->second.sApciPoiner.Interface.nAutoUpCylSstnStatus = m_nCylSstnStatus;
        }
        CMessageLog	*FlowLog = new CMessageLog;
        if(FlowLog == NULL){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Start()] stn[%s] new FlowLog 失败",itStnLink->first.c_str());
            continue;
        }else{
            itStnLink->second.sApciPoiner.pFlowLog = FlowLog;
            itStnLink->second.sApciPoiner.pFlowLog->Close();
            itStnLink->second.sApciPoiner.pFlowLog->SetLogLevel(m_LogCfg.nLogLevel);
            string str = m_LogCfg.strLogPath+"/"+itStnLink->first+"/";
            itStnLink->second.sApciPoiner.pFlowLog->SetLogPath(str.c_str());
            itStnLink->second.sApciPoiner.pFlowLog->SetLogSaveDays(m_LogCfg.nLogRrdDay);
            itStnLink->second.sApciPoiner.pFlowLog->Open("Zx104VlanProWay");
            itStnLink->second.sApciPoiner.pFlowLog->FormatAdd(CLogFile::trace,"Zx104VlanProWay 日志保留天数为:%d.",m_LogCfg.nLogRrdDay);
            itStnLink->second.sApciPoiner.pFlowLog->FormatAdd(CLogFile::trace,"Zx104VlanProWay 日志路徑:%s.",str.c_str());
            
        }
        CMessageLog	*MessageLog = new CMessageLog;
        if(MessageLog == NULL){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Start()] stn[%s] new MessageLog 失败",itStnLink->first.c_str());
            continue;
        }else{
            itStnLink->second.sApciPoiner.pMessageLog = MessageLog;
            itStnLink->second.sApciPoiner.pMessageLog->Close();
            itStnLink->second.sApciPoiner.pMessageLog->SetLogLevel(m_LogCfg.nLogLevel);
            string str=m_LogCfg.strLogPath+"/"+itStnLink->first+"/";
            itStnLink->second.sApciPoiner.pMessageLog->SetLogPath(str.c_str());
            itStnLink->second.sApciPoiner.pMessageLog->SetLogSaveDays(m_LogCfg.nLogRrdDay);
            itStnLink->second.sApciPoiner.pMessageLog->Open("104MessageLogs");
            itStnLink->second.sApciPoiner.pMessageLog->FormatAdd(CLogFile::trace,"104MessageLogs 报文保留天数为:%d.",m_LogCfg.nLogRrdDay);
            itStnLink->second.sApciPoiner.pMessageLog->FormatAdd(CLogFile::trace,"104MessageLogs 日志路徑:%s.",str.c_str());
        }
        CMessageLog	*MMsFlowLog = new CMessageLog;
        if(MMsFlowLog == NULL){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Start()] stn[%s] new MessageLog 失败",itStnLink->first.c_str());
            continue;
        }else{
            itStnLink->second.sApciPoiner.pFlowMMSLog = MMsFlowLog;
            itStnLink->second.sApciPoiner.pFlowMMSLog->Close();
            itStnLink->second.sApciPoiner.pFlowMMSLog->SetLogLevel(m_LogCfg.nLogLevel);
            string str=m_LogCfg.strLogPath+"/"+itStnLink->first+"/";
            itStnLink->second.sApciPoiner.pFlowMMSLog->SetLogPath(str.c_str());
            itStnLink->second.sApciPoiner.pFlowMMSLog->SetLogSaveDays(m_LogCfg.nLogRrdDay);
            itStnLink->second.sApciPoiner.pFlowMMSLog->Open("MMsAnalyzerLogs");
            itStnLink->second.sApciPoiner.pFlowMMSLog->FormatAdd(CLogFile::trace,"MMsAnalyzerLogs 报文保留天数为:%d.",m_LogCfg.nLogRrdDay);
        }
        CXJMsgCaster* pMsgCaster = new CXJMsgCaster(itStnLink->first,"",62404);
        if(pMsgCaster == NULL){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Start()] stn[%s] new CXJ104Len2CpuMsgAttach 失败",itStnLink->first.c_str());
            continue;
        }else{
            itStnLink->second.sApciPoiner.pMsgCaster = pMsgCaster;
        }
        //物理连接层
        CXJ104APCIHandler *p104APCIHandler = new CXJ104APCIHandler(
                                        &(*itStnLink->second.sApciPoiner.pMsg104AttachFactory),
                                        &itStnLink->second.sApciPoiner.Interface,
                                        &(*itStnLink->second.sApciPoiner.pFlowLog),
                                        &(*itStnLink->second.sApciPoiner.pMessageLog),
                                        &(*itStnLink->second.sApciPoiner.pMsgCaster),
                                        itStnLink->second.strMyPrivKey,itStnLink->second.strMyPubKey,itStnLink->second.strStnPubKey,
                                        itStnLink->second.strMyIP,sCtlKeyCfg.nDoSM2);
        if(p104APCIHandler == NULL){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Start()] stn[%s] new p104APCIHandler 失败",itStnLink->first.c_str());
            continue;
        }else{
            itStnLink->second.sApciPoiner.pAPCIHander = p104APCIHandler;
        }
        //sttp转换asdu--  
        //CHuNTc103ASDUHander(&itStnLink->second.sApciPoiner.Interface,NULL,&(*itStnLink->second.sApciPoiner.pFlowLog),NULL);
       m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::PreAPCI]CHuNTc103ASDUHandler::pFlowASDUHandler stn[%s]",itStnLink->second.sApciPoiner.Interface.pszStationId);
        CHuNTc103ASDUHandler *pFlowASDUHandler = new CHuNTc103ASDUHandler(  
                                            &itStnLink->second.sApciPoiner.Interface,
                                            NULL,
                                            &(*itStnLink->second.sApciPoiner.pFlowLog),
                                            NULL,m_strSftpHomePath  );
        if(pFlowASDUHandler == NULL){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Start()] stn[%s] new pFlowASDUHandler 失败",itStnLink->first.c_str());
            continue;
        }else{
            itStnLink->second.sApciPoiner.pFlowASDUHander = pFlowASDUHandler;
        }
        //站级sttp转asdu维护 业务流
        CXJPro103ClientWay *pFlow = new CXJPro103ClientWay(
                                            itStnLink->second.sApciPoiner.Interface,
                                            *itStnLink->second.sApciPoiner.pAPCIHander,
                                            *itStnLink->second.sApciPoiner.pFlowASDUHander,
                                            *itStnLink->second.sApciPoiner.pFlowLog,
                                            &(*itStnLink->second.sApciPoiner.pFlowMMSLog),
                                            m_strComtradePath,
                                            itStnLink->second.mapStnDevId,
                                            strStnMgrPtID,
                                            sCtlKeyCfg.nDoCrcChk,
                                            sCtlKeyCfg.nUseDb,
                                            sCtlKeyCfg.nUseMMS,
                                            m_strScdPath,
                                            m_pDBAcess);
        if(pFlow == NULL){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Start()] stn[%s] new CXJPro103ClientWay 失败",itStnLink->first.c_str());
            continue;
        }else{
            itStnLink->second.sApciPoiner.pStnFlow = pFlow;
            pFlow->SetWorkArea(m_workArea);
            pFlow->SetModFileCallType(m_nModFileCallType);
            m_mapFntSta[itStnLink->first] = -1; // 初始化连接夶为未知
        }
        
        //start线程        
        int r= itStnLink->second.sApciPoiner.pStnFlow->Run();
        if(0 != r){
            m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::Start()] pStnFlow->Run() failed[%d]",r);
            Free104StnRes(itStnLink->second.sApciPoiner);
        }
    }
    
    
    return 0;
}

int CTaskMngr::Pre102()
{
    //<ip1,stnId1>
    for(map<string,string>::iterator it=sListen61850.mapBindIpStnId.begin();it!=sListen61850.mapBindIpStnId.end();it++){
        if(it->first.empty()){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Pre102()] listen IP[%s]异常跳过 Port[%d] threadNum[%d]",it->first.c_str(), sListen61850.nPort, LISTEN_DO_THREAD_NUM);
            continue;
        }
        CMessageLog	*MessageLog = new CMessageLog;
        if(MessageLog == NULL){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Pre102()] stn[%s]ip[%s] new MessageLog 失败",it->second.c_str(),it->first.c_str());
            continue;
        }else{
            MessageLog->Close();
            MessageLog->SetLogLevel(m_LogCfg.nLogLevel);
            string str=m_LogCfg.strLogPath+"/"+it->second+"/"+it->first+"/";//stn/ip/
            MessageLog->SetLogPath(str.c_str());
            MessageLog->SetLogSaveDays(m_LogCfg.nLogRrdDay);
            MessageLog->Open("102MessageLogs");
            MessageLog->FormatAdd(CLogFile::trace,"102MessageLogs 报文保留天数为:%d.",m_LogCfg.nLogRrdDay);
        }
        push* p_102Push = new push(MessageLog,m_pExit);//一定要new的不然this不对
        map<string,pthread_t > mapThreadIds;
        mapThreadIds.clear();
        int ret = p_102Push->start(it->first, sListen61850.nPort, it->second , mapThreadIds); 
        if(ret == 0 ){
            map102Push.insert(make_pair(it->second,p_102Push));
            map102PushTheadId.insert(make_pair(it->second,mapThreadIds));
            for(map<string,pthread_t >::iterator itTh=mapThreadIds.begin();itTh!=mapThreadIds.end();itTh++){
                m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::Pre102()] listen IP[%s] Port[%d] threadNum[%d]p_102Push[%p] Thread[%s]nThreadId[%ld]",it->first.c_str(), 
                    sListen61850.nPort, LISTEN_DO_THREAD_NUM,&*p_102Push,itTh->first.c_str(),itTh->second);
            }
        }else{
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Pre102()] listen IP[%s] Port[%d] threadNum[%d]p_102Push[%p]  启动失败,ret[%d]",it->first.c_str(), 
                    sListen61850.nPort, LISTEN_DO_THREAD_NUM,&*p_102Push,ret);
        }
    }
    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::Pre102()] map102Push[%d]",map102Push.size());

}

 int  CTaskMngr::Pre103()
 {
     for(map<string,string>::iterator it=sListen61850.mapBindIpStnId.begin();it!=sListen61850.mapBindIpStnId.end();it++){
        if(it->first.empty()){
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Pre103()] listen IP[%s]异常跳过 Port[%d] threadNum[%d]",it->first.c_str(), sListen61850.nPort, LISTEN_DO_THREAD_NUM);
            continue;
        }
	// lmy add 
	CMessageLog	*MessageLog103 = new CMessageLog;
	if(MessageLog103 == NULL){
	m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Pre103()] stn[%s]ip[%s] new MessageLog 失败",it->second.c_str(),it->first.c_str());
		continue;
	}else{
		MessageLog103->Close();
		MessageLog103->SetLogLevel(m_LogCfg.nLogLevel);
		string str=m_LogCfg.strLogPath+"/"  +it->second+"/"+it->first+"/";//stn/ip/
		MessageLog103->SetLogPath(str.c_str());
		MessageLog103->SetLogSaveDays(m_LogCfg.nLogRrdDay);
		MessageLog103->Open("103MessageLogs");
		MessageLog103->FormatAdd(CLogFile::trace,"103MessageLogs 报文保留天数为:%d.",m_LogCfg.nLogRrdDay);
	}

        std::string sStn = it->second;
        SecDevFlowMoudle::getInstanse()->addDevManageFlow(sStn);
	SecDevFlowMoudle::getInstanse()->_init(MessageLog103,m_pExit,sStn);
        SecDevFlowMoudle::getInstanse()->setMaxTimeOut(sTimeCfg.nAuthTime); 
        m_pLogFile->FormatAdd(CLogFile::trace,"[%s] 103数据管理模块初始化成功",it->second.c_str());
        
        push103* p_103Push = new push103(MessageLog103,m_pExit);//一定要new的不然this不对
        map<string,pthread_t > map103ThreadIds;
        map103ThreadIds.clear();
       int ret = p_103Push->start(it->first, sListen61850.n103Port, it->second , map103ThreadIds); 
        if(ret == 0 ){
            map103Push.insert(make_pair(it->second,p_103Push));
            map103PushTheadId.insert(make_pair(it->second,map103ThreadIds));
            for(map<string,pthread_t >::iterator itTh=map103ThreadIds.begin();itTh!=map103ThreadIds.end();itTh++){
                m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::Pre103()] listen IP[%s] Port[%d] threadNum[%d]p_103Push[%p] Thread[%s]nThreadId[%ld]",it->first.c_str(), 
                    sListen61850.n103Port, LISTEN_DO_THREAD_NUM,&*p_103Push,itTh->first.c_str(),itTh->second);
            }
            
        SecDevFlowMoudle::getInstanse()->regSendToFront(it->second,p_103Push,&push103::send103ToFront); // 注册回调
        SecDevFlowMoudle::getInstanse()->regNotifyDevStatu(it->second,p_103Push,&push103::notifyFrontDevState); // 注册回调
        m_pLogFile->FormatAdd(CLogFile::trace,"[%s]  push103::send103ToFront 注册回调完毕",it->second.c_str());
       
        }else{
            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::Pre103()] listen IP[%s] Port[%d] threadNum[%d]p_103Push[%p]  启动失败,ret[%d]",it->first.c_str(), 
                    sListen61850.n103Port, LISTEN_DO_THREAD_NUM,&*p_103Push,ret);
        }

    }
    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::Pre103()] map103Push[%d]",map102Push.size());
 }

void CTaskMngr::regisAsdu200Tc103()
{
    for(map<string,STN_CFG>::iterator itStnLink=mapStnLinkCfg.begin();itStnLink!=mapStnLinkCfg.end();itStnLink++)
    {
	if(NULL != itStnLink->second.sApciPoiner.pStnFlow)
	{
            // 注册数据透传函数
            std::string strStn = itStnLink->first;
	    SecDevFlowMoudle::getInstanse()->regSendToStnFun(strStn,itStnLink->second.sApciPoiner.pStnFlow,&CXJPro103ClientWay::SendAsdu200Tc103); // 注册回调
            
            // 注册设备连接函数
	    SecDevFlowMoudle::getInstanse()->regSendConnectFun(strStn,itStnLink->second.sApciPoiner.pStnFlow,&CXJPro103ClientWay::SendAsdu200Conn103); // 注册回调
            SecDevFlowMoudle::getInstanse()->regSendCloseContFun(strStn,itStnLink->second.sApciPoiner.pStnFlow,&CXJPro103ClientWay::SendAsdu200Close103Conn); // 注册回调
	}
    }
}
int CTaskMngr::LoadConfigFile()
{
    char chTemp[255] = "";
    
    bzero(chTemp,255);
    GetIniKey(Zcs104VlanSrvINI, "LOG", "LogDays", chTemp);
    m_LogCfg.nLogRrdDay = (atoi(chTemp)==0) ? 3 : atoi(chTemp);
   printf("读取配置[%s] nLogRrdDay[%d]\n",Zcs104VlanSrvINI,m_LogCfg.nLogRrdDay);
    bzero(chTemp,255);
	GetIniKey(Zcs104VlanSrvINI, "LOG", "LogLevel", chTemp);
    m_LogCfg.nLogLevel = (atoi(chTemp)==0) ? 3 : atoi(chTemp);
   printf("读取配置[%s] nLogLevel[%d]\n",Zcs104VlanSrvINI,m_LogCfg.nLogLevel);
    bzero(chTemp,255);
	GetIniKey(Zcs104VlanSrvINI, "LOG", "LogRootPath", chTemp);
    m_LogCfg.strLogPath = chTemp;
   printf("读取配置[%s] strLogPath[%s]\n",Zcs104VlanSrvINI,m_LogCfg.strLogPath.c_str());
    bzero(chTemp,255);
	GetIniKey(Zcs104VlanSrvINI, "LOG", "LogRootPath", chTemp);
    m_LogCfg.nLogMaxSize = (atoi(chTemp)==0) ? (1024*1024) : atoi(chTemp);
   printf("读取配置[%s] nLogMaxSize[%d]\n",Zcs104VlanSrvINI,m_LogCfg.nLogMaxSize);
    bzero(chTemp,255);
	GetIniKey(Zcs104VlanSrvINI, "LOG", "ShowDebugLog", chTemp);
    m_LogCfg.bShowDebug = (atoi(chTemp)==1) ? true : false;
   printf("读取配置[%s] ShowDebugLog[%d]\n",Zcs104VlanSrvINI,m_LogCfg.bShowDebug);
    
    bzero(chTemp,255);
	GetIniKey(Zcs104VlanSrvINI, "DRAGON_PATH", "ComtradePath", chTemp);
    m_strComtradePath = chTemp;
   printf("读取配置[%s] m_strComtradePath[%s]\n",Zcs104VlanSrvINI,m_strComtradePath.c_str());
    bzero(chTemp,255);
	GetIniKey(Zcs104VlanSrvINI, "DRAGON_PATH", "DragonHomePath", chTemp);
    m_strSftpHomePath = chTemp;
   printf("读取配置[%s] m_strSftpHomePath[%s]\n",Zcs104VlanSrvINI,m_strSftpHomePath.c_str());
   
   bzero(chTemp,255);
	GetIniKey(Zcs104VlanSrvINI, "DRAGON_PATH", "ScdPath", chTemp);
    m_strScdPath = chTemp;
   printf("读取配置[%s] m_strScdPath[%s]\n",Zcs104VlanSrvINI,m_strScdPath.c_str());
    
   
    bzero(chTemp,255);
	GetIniKey(Zcs104VlanSrvINI, "NET", "BindIpNum", chTemp);
	int nIpNum = atoi(chTemp);
    printf("读取配置[%s] BindIpNum[%d]\n",Zcs104VlanSrvINI,nIpNum);
    for(int i=1;i<=nIpNum;i++){
        char chTmpNum[10];
        bzero(chTmpNum,10);
        sprintf(chTmpNum,"BindIp%d",i);
        bzero(chTemp,255);
        GetIniKey(Zcs104VlanSrvINI, "NET", chTmpNum, chTemp);
        string strIp = chTemp;
       printf("读取配置[%s] %s[%s]\n",Zcs104VlanSrvINI,chTmpNum,strIp.c_str());
       
        bzero(chTmpNum,10);
        sprintf(chTmpNum,"StnId%d",i);
        bzero(chTemp,255);
        GetIniKey(Zcs104VlanSrvINI, "NET", chTmpNum, chTemp);
        string strStnId = chTemp;
       printf("读取配置[%s] %s[%s]\n",Zcs104VlanSrvINI,chTmpNum,strStnId.c_str());
       
       if(strIp.empty()||strStnId.empty()){
           printf("读取配置[%s] strIp[%s]strStnId[%s] 异常，退出程序\n",Zcs104VlanSrvINI,strIp.c_str(),strStnId.c_str());
           return -1;
       }
       
       if(!m_StnRun.empty()){
            if(0!=strcmp(strStnId.c_str(),m_StnRun.c_str())){
                printf("[CTaskMngr::LoadConfigFile]  m_StnRun[%s]单厂站进程运行，配置文件mapBindIpStnId跳过[%s][%s]\n",m_StnRun.c_str(),strStnId.c_str(),strIp.c_str());
                continue;
            }else{
                sListen61850.mapBindIpStnId.insert(make_pair(strIp,strStnId));
                printf("mapBindIpStnId insert[%s][%s] \n",strIp.c_str(),strStnId.c_str());
            }
        }else{
            sListen61850.mapBindIpStnId.insert(make_pair(strIp,strStnId));
            printf("mapBindIpStnId insert[%s][%s] \n",strIp.c_str(),strStnId.c_str());
        }
    }
    bzero(chTemp,255);
	GetIniKey(Zcs104VlanSrvINI, "NET", "ListenPort", chTemp);
    sListen61850.strPort= chTemp;
    sListen61850.nPort = (atoi(chTemp)==0) ? 102 : atoi(chTemp);
   printf("读取配置[%s] strPort[%s]\n",Zcs104VlanSrvINI,sListen61850.strPort.c_str());

   // lmy add
   bzero(chTemp,255);
   GetIniKey(Zcs104VlanSrvINI, "NET", "Listen103Port", chTemp);
   sListen61850.str103Port= chTemp;
   sListen61850.n103Port = (atoi(chTemp)==0) ? 103 : atoi(chTemp);
   printf("读取配置[%s] strPort[%s]\n",Zcs104VlanSrvINI,sListen61850.str103Port.c_str());
   // end lmy add
    
    bzero(chTemp,255);
    GetIniKey(Zcs104VlanSrvINI, "TIMEOUT", "AuthTime", chTemp);
    sTimeCfg.nAuthTime = (atoi(chTemp)==0) ? 30 : atoi(chTemp);
    printf("读取配置[%s] nAuthTime[%d]\n",Zcs104VlanSrvINI,sTimeCfg.nAuthTime);
   
    
    bzero(chTemp,255);
    GetIniKey(Zcs104VlanSrvINI, "CTL_KEY", "DoSM2Chk", chTemp);
    sCtlKeyCfg.nDoSM2 = atoi(chTemp);
    printf("读取配置[%s] DoSM2Chk[%d]\n",Zcs104VlanSrvINI,sCtlKeyCfg.nDoSM2);
    bzero(chTemp,255);
    GetIniKey(Zcs104VlanSrvINI, "CTL_KEY", "DoCrcChk", chTemp);
    sCtlKeyCfg.nDoCrcChk = atoi(chTemp);
    printf("读取配置[%s] DoCrcChk[%d]\n",Zcs104VlanSrvINI,sCtlKeyCfg.nDoCrcChk);
    sCtlKeyCfg.nUseDb = 1;
   
    bzero(chTemp,255);
    GetIniKey(Zcs104VlanSrvINI, "CTL_KEY", "UseMMSanalyze", chTemp);
    sCtlKeyCfg.nUseMMS = atoi(chTemp);
    if(sCtlKeyCfg.nUseDb!=1){
        printf("读取配置[%s] UseMMSanalyze[%d] 因为UseDbGetPtID不为1所以 当0\n",Zcs104VlanSrvINI,sCtlKeyCfg.nUseMMS);
        sCtlKeyCfg.nUseMMS = 0;
    }
    printf("读取配置[%s] 在用的是 UseMMSanalyze[%d]\n",Zcs104VlanSrvINI,sCtlKeyCfg.nUseMMS);
   
    //20240621 lmy 获取运行区域
    bzero(chTemp,255);
    GetIniKey(ZAS_BUS_DEF_CONFIG_INI, "SAFE_AREA", "work_part", chTemp);
    m_workArea = (atoi(chTemp)==0) ? 2 : atoi(chTemp);
    printf("读取配置安全运行区[%s] m_workArea[%d]\n",ZAS_BUS_DEF_CONFIG_INI,m_workArea);
    
    bzero(chTemp,255);
    GetIniKey(Zcs104VlanSrvINI, "CTL_KEY", "ModelFileCallType", chTemp);
    m_nModFileCallType = atoi(chTemp);
    printf("读取配置[%s] 在用的是 ModelFileCallType[%d]\n",Zcs104VlanSrvINI,m_nModFileCallType);
    // end lmy
   
   
    //TODO  最好能命令行动态热加载
    bzero(chTemp,255);
    GetIniKey(Zcs104VlanSrvINI, "STN_LINK", "total_pair_num", chTemp);
    int nTotalNum = atoi(chTemp);
    printf("读取配置[%s] total_pair_num[%d]\n",Zcs104VlanSrvINI,nTotalNum);
    for(int i=1;i<=nTotalNum;i++){
        char chTmpNum[100];
        STN_CFG sStnCfg;
        sStnCfg.nNo = i;
        //
        bzero(chTmpNum,100);
        sprintf(chTmpNum,"StnID%d",i);
        bzero(chTemp,255);
        GetIniKey(Zcs104VlanSrvINI, "STN_LINK", chTmpNum, chTemp);
        sStnCfg.strStnId= chTemp;
        snprintf(sStnCfg.cStnId,sizeof(sStnCfg.cStnId),"%s",sStnCfg.strStnId.c_str());
        printf("读取配置[%s] %s[%s]\n",Zcs104VlanSrvINI,chTmpNum,sStnCfg.strStnId.c_str());
        //
        bzero(chTmpNum,100);
        sprintf(chTmpNum,"MyIp%d",i);
        bzero(chTemp,255);
        GetIniKey(Zcs104VlanSrvINI, "STN_LINK", chTmpNum, chTemp);
        sStnCfg.strMyIP= chTemp;
        printf("读取配置[%s] %s[%s]\n",Zcs104VlanSrvINI,chTmpNum,sStnCfg.strMyIP.c_str());
        //
        bzero(chTmpNum,100);
        sprintf(chTmpNum,"StnIp%d",i);
        bzero(chTemp,255);
        GetIniKey(Zcs104VlanSrvINI, "STN_LINK", chTmpNum, chTemp);
        sStnCfg.strStnIP= chTemp;
        printf("读取配置[%s] %s[%s]\n",Zcs104VlanSrvINI,chTmpNum,sStnCfg.strStnIP.c_str());
       //
        bzero(chTmpNum,100);
        sprintf(chTmpNum,"StnIpChnB%d",i);
        bzero(chTemp,255);
        GetIniKey(Zcs104VlanSrvINI, "STN_LINK", chTmpNum, chTemp);
        sStnCfg.strStnChnBIP= chTemp;
        printf("读取配置[%s] %s[%s]\n",Zcs104VlanSrvINI,chTmpNum,sStnCfg.strStnChnBIP.c_str());
        //
        bzero(chTmpNum,100);
        sprintf(chTmpNum,"StnPort%d",i);
        bzero(chTemp,255);
        GetIniKey(Zcs104VlanSrvINI, "STN_LINK", chTmpNum, chTemp);
        sStnCfg.strStnPort= chTemp;
        sStnCfg.nStnPort = (atoi(chTemp)==0) ? 2404 : atoi(chTemp);
       printf("读取配置[%s] %s[%s]\n",Zcs104VlanSrvINI,chTmpNum,sStnCfg.strStnPort.c_str());
        //
        bzero(chTmpNum,100);
        sprintf(chTmpNum,"SM2StnPubFile%d",i);
        bzero(chTemp,255);
        GetIniKey(Zcs104VlanSrvINI, "STN_LINK", chTmpNum, chTemp);
        sStnCfg.strStnPubKey= chTemp;
       printf("读取配置[%s] %s[%s]\n",Zcs104VlanSrvINI,chTmpNum,sStnCfg.strStnPubKey.c_str());
        //
        bzero(chTmpNum,100);
        sprintf(chTmpNum,"SM2MyPrvFile%d",i);
        bzero(chTemp,255);
        GetIniKey(Zcs104VlanSrvINI, "STN_LINK", chTmpNum, chTemp);
        sStnCfg.strMyPrivKey= chTemp;
       printf("读取配置[%s] %s[%s]\n",Zcs104VlanSrvINI,chTmpNum,sStnCfg.strMyPrivKey.c_str());
        //
        bzero(chTmpNum,100);
        sprintf(chTmpNum,"SM2MyPubFile%d",i);
        bzero(chTemp,255);
        GetIniKey(Zcs104VlanSrvINI, "STN_LINK", chTmpNum, chTemp);
        sStnCfg.strMyPubKey= chTemp;
       printf("读取配置[%s] %s[%s]\n",Zcs104VlanSrvINI,chTmpNum,sStnCfg.strMyPubKey.c_str());
       bzero(chTmpNum, 100);
       sprintf(chTmpNum, "StnConnectChgSec%d", i);
       bzero(chTemp, 255);
       GetIniKey(Zcs104VlanSrvINI, "STN_LINK", chTmpNum, chTemp);
       sStnCfg.nStnConnectChgSec = (atoi(chTemp) == 0) ? 5*60 : atoi(chTemp);;
       printf("读取配置[%s] %s[%s]\n", Zcs104VlanSrvINI, chTmpNum, sStnCfg.strMyPubKey.c_str());
        
        snprintf(sStnCfg.cDragonPath,sizeof(sStnCfg.cDragonPath),m_strComtradePath.c_str());
        if(!m_StnRun.empty()){
            if(0!=strcmp(sStnCfg.strStnId.c_str(),m_StnRun.c_str())){
                printf("[CTaskMngr::LoadConfigFile]  m_StnRun[%s]单厂站进程运行，配置文件跳过[%s]\n",m_StnRun.c_str(),sStnCfg.strStnId.c_str());
                continue;
            }else{
                mapStnLinkCfg.insert( make_pair(sStnCfg.strStnId.c_str(),sStnCfg) );
                printf("mapStnLinkCfg insert[%s] \n",sStnCfg.strStnId.c_str());
            }
        }else{
            mapStnLinkCfg.insert( make_pair(sStnCfg.strStnId.c_str(),sStnCfg) );
            printf("mapStnLinkCfg insert[%s] \n",sStnCfg.strStnId.c_str());
        }
    }
    
   return 0;
}

int CTaskMngr::ReLoadConfigFile()
{
    char chTemp[255] = "";
    bzero(chTemp,255);
	GetIniKey(Zcs104VlanSrvINI, "STN_LINK", "total_pair_num", chTemp);
	int nTotalNum = atoi(chTemp);
   printf("读取配置[%s] total_pair_num[%d]\n",Zcs104VlanSrvINI,nTotalNum);
    for(int i=1;i<=nTotalNum;i++){
        char chTmpNum[100];
        STN_CFG sStnCfg;
        sStnCfg.nNo = i;
        //
        bzero(chTmpNum,100);
        sprintf(chTmpNum,"StnID%d",i);
        bzero(chTemp,255);
        GetIniKey(Zcs104VlanSrvINI, "STN_LINK", chTmpNum, chTemp);
        sStnCfg.strStnId= chTemp;
        snprintf(sStnCfg.cStnId,sizeof(sStnCfg.cStnId),"%s",sStnCfg.strStnId.c_str());
       printf("读取配置[%s] %s[%s]\n",Zcs104VlanSrvINI,chTmpNum,sStnCfg.strStnId.c_str());
        //
        bzero(chTmpNum,100);
        sprintf(chTmpNum,"MyIp%d",i);
        bzero(chTemp,255);
        GetIniKey(Zcs104VlanSrvINI, "STN_LINK", chTmpNum, chTemp);
        sStnCfg.strMyIP= chTemp;
       printf("读取配置[%s] %s[%s]\n",Zcs104VlanSrvINI,chTmpNum,sStnCfg.strMyIP.c_str());
        //
        bzero(chTmpNum,100);
        sprintf(chTmpNum,"StnIp%d",i);
        bzero(chTemp,255);
        GetIniKey(Zcs104VlanSrvINI, "STN_LINK", chTmpNum, chTemp);
        sStnCfg.strStnIP= chTemp;
       printf("读取配置[%s] %s[%s]\n",Zcs104VlanSrvINI,chTmpNum,sStnCfg.strStnIP.c_str());
        //
        bzero(chTmpNum,100);
        sprintf(chTmpNum,"StnPort%d",i);
        bzero(chTemp,255);
        GetIniKey(Zcs104VlanSrvINI, "STN_LINK", chTmpNum, chTemp);
        sStnCfg.strStnPort= chTemp;
        sStnCfg.nStnPort = (atoi(chTemp)==0) ? 2404 : atoi(chTemp);
       printf("读取配置[%s] %s[%s]\n",Zcs104VlanSrvINI,chTmpNum,sStnCfg.strStnPort.c_str());
        //
        bzero(chTmpNum,100);
        sprintf(chTmpNum,"SM2StnPubFile%d",i);
        bzero(chTemp,255);
        GetIniKey(Zcs104VlanSrvINI, "STN_LINK", chTmpNum, chTemp);
        sStnCfg.strStnPubKey= chTemp;
       printf("读取配置[%s] %s[%s]\n",Zcs104VlanSrvINI,chTmpNum,sStnCfg.strStnPubKey.c_str());
        //
        bzero(chTmpNum,100);
        sprintf(chTmpNum,"SM2MyPrvFile%d",i);
        bzero(chTemp,255);
        GetIniKey(Zcs104VlanSrvINI, "STN_LINK", chTmpNum, chTemp);
        sStnCfg.strMyPrivKey= chTemp;
       printf("读取配置[%s] %s[%s]\n",Zcs104VlanSrvINI,chTmpNum,sStnCfg.strMyPrivKey.c_str());
        //
        bzero(chTmpNum,100);
        sprintf(chTmpNum,"SM2MyPubFile%d",i);
        bzero(chTemp,255);
        GetIniKey(Zcs104VlanSrvINI, "STN_LINK", chTmpNum, chTemp);
        sStnCfg.strMyPubKey= chTemp;
       printf("读取配置[%s] %s[%s]\n",Zcs104VlanSrvINI,chTmpNum,sStnCfg.strMyPubKey.c_str());
        
        snprintf(sStnCfg.cDragonPath,sizeof(sStnCfg.cDragonPath),m_strComtradePath.c_str());
        if(!m_StnRun.empty()){
            if(0!=strcmp(sStnCfg.strStnId.c_str(),m_StnRun.c_str())){
                printf("[CTaskMngr::LoadConfigFile]  m_StnRun[%s]单厂站进程运行，配置文件跳过[%s]\n",m_StnRun.c_str(),sStnCfg.strStnId.c_str());
                continue;
            }else{
                if(mapStnLinkCfg.find(sStnCfg.strStnId)==mapStnLinkCfg.end()){
                    mapStnLinkCfg.insert( make_pair(sStnCfg.strStnId.c_str(),sStnCfg) );
                    m_pLogFile->FormatAdd(CLogFile::trace,"mapStnLinkCfg增加 insert[%s] \n",sStnCfg.strStnId.c_str());
                }
            }
        }else{
            if(mapStnLinkCfg.find(sStnCfg.strStnId)==mapStnLinkCfg.end()){
                mapStnLinkCfg.insert( make_pair(sStnCfg.strStnId.c_str(),sStnCfg) );
                m_pLogFile->FormatAdd(CLogFile::trace,"mapStnLinkCfg增加 insert[%s] \n",sStnCfg.strStnId.c_str());
            }
        }
    }
   
   // 20231220 lmy add 
   bzero(chTemp,255);
   GetIniKey(Zcs104VlanSrvINI, "LOG", "ShowDebugLog", chTemp);
   bool bShow = (atoi(chTemp)==1) ? true : false;
   if(bShow != m_LogCfg.bShowDebug)
   {
       logChange(bShow);
       m_LogCfg.bShowDebug = (atoi(chTemp)==1) ? true : false;
       m_pLogFile->FormatAdd(CLogFile::trace,"读取配置[Zcs104VlanSrv.ini] bShow[%d]",bShow?1:0);
   }
   // end lmy
    
    return 0;
}

bool CTaskMngr::InitLogFile()
{
    m_pLogFile = new CMessageLog();
    if (NULL == m_pLogFile)
	{
		printf("日志对象指针分配内存失败！");
		return -1;
	}
	// first close log file
	m_pLogFile->Close();

	// set log level
	m_pLogFile->SetLogLevel(m_LogCfg.nLogLevel);
	m_pLogFile->SetLogSaveDays(m_LogCfg.nLogRrdDay);
    m_pLogFile->SetLogSize(m_LogCfg.nLogMaxSize);
	// set log path
	char cMyPath[512]="";
    if(m_StnRun.empty()){
        sprintf(cMyPath,"%s/%s",m_LogCfg.strLogPath.c_str(), EXE_NAME);
    }else{
        sprintf(cMyPath,"%s/%s/%s",m_LogCfg.strLogPath.c_str(),EXE_NAME,m_StnRun.c_str());
    }
	m_LogCfg.strLogPath = cMyPath;
	m_pLogFile->SetLogPath(cMyPath);
	
	// open log file
	if( !m_pLogFile->Open(EXE_NAME) )
	{
		return false;
	}	

	return true;
}

bool CTaskMngr::GetLogSetFromDB()
{
	bool bResult(false);
	if (m_pDBAcess == NULL)
	{
        m_pLogFile->Add("[CTaskMngr::GetLogSetFromDB()]数据库指针为空",CLogFile::error);
		return bResult;
	}
	
	CMemSet pMemSet;
	char sError[255] = "";
	char strLog[255] = "";
	
	SQL_DATA pSqlData;
	pSqlData.Conditionlist.clear();
	pSqlData.Fieldlist.clear();
	
	AddField(pSqlData,"log_day",EX_STTP_DATA_TYPE_INT);
    AddField(pSqlData,"log_level",EX_STTP_DATA_TYPE_INT);
    AddField(pSqlData,"log_path",EX_STTP_DATA_TYPE_STRING);
    //AddField(pSqlData,"callall",EX_STTP_DATA_TYPE_INT);

	REALDATA_CONDITION pCondition;
	pCondition.IsUse = true;	
	
	try
	{
		if (m_pDBAcess->RDSelect(EX_STTP_INFO_LOCAL_CFG,pSqlData,pCondition,sError,&pMemSet))
		{
			if (pMemSet.GetMemRowNum() > 0)
			{
				m_LogCfg.nLogRrdDay = atoi( pMemSet.GetValue(UINT(0)) );
                m_LogCfg.nLogLevel = atoi( pMemSet.GetValue(UINT(1)) );
                m_LogCfg.strLogPath = pMemSet.GetValue(UINT(2));
                //sTimeCfg.nCommStatCyle = atoi(pMemSet.GetValue(UINT(3)));
				bResult = true;
			} 
			else
			{
				bzero(strLog,255);
				m_pLogFile->Add("[CTaskMngr::GetLogSetFromDB()]读取log参数失败,结果数据集为空",CLogFile::error);
				bResult = false;
			}
		}
		else{
			bzero(strLog,255);
			m_pLogFile->Add("[CTaskMngr::GetLogSetFromDB()]读取log参数失败",CLogFile::error);
			bResult = false;
		}
	}
	catch (...)
	{
		bzero(strLog,255);
		m_pLogFile->Add("[CTaskMngr::GetLogSetFromDB()]读取log参数异常",CLogFile::error);
		bResult = false;
	}	
	return bResult;		
}

 
bool CTaskMngr::InitLogFileFromDb()
{
    if (NULL == m_pLogFile) 
    {
        m_pLogFile->Add("[CTaskMngr::InitLogFileFromDb()]日志对象指针分配内存失败！",CLogFile::error);	
	return false;
    }

    m_pLogFile->Close();

    m_pLogFile->SetLogLevel(m_LogCfg.nLogLevel);
    m_pLogFile->SetLogSaveDays(m_LogCfg.nLogRrdDay);
    m_pLogFile->SetLogSize(m_LogCfg.nLogMaxSize);

	char cMyPath[512]="";
    if(m_StnRun.empty()){
        sprintf(cMyPath,"%s/%s",m_LogCfg.strLogPath.c_str(), EXE_NAME);
    }else{
        sprintf(cMyPath,"%s/%s/%s",m_LogCfg.strLogPath.c_str(),EXE_NAME,m_StnRun.c_str());
    }
	m_LogCfg.strLogPath = cMyPath;
	m_pLogFile->SetLogPath(cMyPath);
	
	// open log file
	if( !m_pLogFile->Open(EXE_NAME) )
	{
		return false;
	}	
    m_pLogFile->Add("[CTaskMngr::InitLogFileFromDb()]设置TB_COMMU_LOCAL_CONFIG的日志参数到日志成功!",CLogFile::trace);	
	return true;
}
int CTaskMngr::GetStnMngrPtIdFromDB()
{
    if ( NULL == m_pDBAcess ) {
		m_pLogFile->FormatAdd(CLogFile::error,"[CXJPro103ClientWay::GetStnMngrPtIdFromDB]数据库操作对象指针为空。");
		return -1;
	}
	CMemSet pMemSet;
	SQL_DATA SqlDate;
	SqlDate.Conditionlist.clear();
	SqlDate.Fieldlist.clear();
    
    char lsStr[250] = "";
    if(m_StnRun.empty()){
        snprintf(lsStr,sizeof(lsStr),"TYPE = 0 "); 
    }else{
        snprintf(lsStr,sizeof(lsStr),"TYPE = 0 and STATION_ID='%s' ",m_StnRun.c_str()); 
    }
    AddCondition(SqlDate,lsStr);
    
        
	AddField(SqlDate,"PT_ID",EX_STTP_DATA_TYPE_STRING);
    AddField(SqlDate,"STATION_ID",EX_STTP_DATA_TYPE_STRING);

	char sError[255] = "";
	try
	{
	    if (true == m_pDBAcess->Select(EX_STTP_INFO_SECDEV_CFG,SqlDate,sError,&pMemSet)) {
            if (0 != pMemSet.GetMemRowNum()) {
                pMemSet.MoveFirst();
                for ( int i=0;i<pMemSet.GetMemRowNum();i++) {
                    string strPtId = pMemSet.GetValue(UINT(0));//
                    string strStnId = pMemSet.GetValue(UINT(1));//
                    map<string,string>::iterator ita = m_mapStnMgrId.find(strPtId);//<ptid,stnid>
                    if(ita == m_mapStnMgrId.end()){
                        m_mapStnMgrId.insert( make_pair(strPtId,strStnId) );
                        m_pLogFile->FormatAdd(CLogFile::trace,"[CXJPro103ClientWay::GetStnMngrPtIdFromDB]找到管理单元ptid[%s]stnid[%s]",strPtId.c_str(),strStnId.c_str());
                    }
                    map<string,string>::iterator itb = m_mapStnMgrPt.find(strStnId);//<stnid,ptid>
                    if(itb == m_mapStnMgrPt.end()){
                        m_mapStnMgrPt.insert( make_pair(strStnId,strPtId) );
                    }else{
                        m_pLogFile->FormatAdd(CLogFile::error,"[CXJPro103ClientWay::GetStnMngrPtIdFromDB]stnid[%s]找到多个管理单元ptid,[%s]替换已有ptid[%s]",
                                strStnId.c_str(),strPtId.c_str(),itb->second.c_str());
                        itb->second = strPtId;
                    }
                    pMemSet.MoveNext();
                }
                return 0;
            }else{
                return 0;
                m_pLogFile->FormatAdd(CLogFile::error,"[CXJPro103ClientWay::GetStnMngrPtIdFromDB]select成功但是结果数量[%d]为0 异常",pMemSet.GetMemRowNum());
            }
		}
		else{
			m_pLogFile->FormatAdd(CLogFile::error,sError);
			return -1;
		}
	}
	catch (...)
	{
		sprintf(sError,"[CXJPro103ClientWay::GetStnMngrPtIdFromDB]读取信息时发生异常.");
		m_pLogFile->FormatAdd(CLogFile::error,sError);
		return -2;
	}
	return -3;	
}
bool CTaskMngr::IsStnMgr(string strPtId,string &strStn)
{
    map<string,string>::iterator ita = m_mapStnMgrId.find(strPtId);
    if(ita != m_mapStnMgrId.end()){
        strStn = ita->second;
        return true;
    }
    return false;
}


/*************************************************************
 函 数 名：	  任务管理线程
 功能概要：   每2ms执行一次线程，并打印异常返回
 返 回 值:    线程的this指针
***************************************************************/
THREAD_FUNC CTaskMngr::_TaskMngrThread(void *arg)
{
    CTaskMngr* pTaskMngr = (CTaskMngr*)arg;
    if(pTaskMngr == 0) return arg;
    pthread_t ulMyThreadId = pthread_self();
    pTaskMngr->m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::_TaskMngrThread()] 线程ID %d ", syscall(SYS_gettid));
    while( !(*pTaskMngr->m_pExit) )
    {
        int ret=0;
        ret=pTaskMngr->DoTaskManager(ulMyThreadId);
        if(ret!=0) {pTaskMngr->m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::_TaskMngrThread()] 异常返回[%d]",ret);}
        MySleep(1);
    }
    pTaskMngr->m_pLogFile->FormatAdd(CLogFile::trace,"[%s]主进程线程 Exit,exception,error[%s]code=[%d]  [%s] [%d]",__FUNCTION__,strerror(errno),errno,__FILE__,__LINE__);
    return arg;
}

/*************************************************************
 函 数 名：	 主管理线程
 功能概要：   检查故障报告，检查文件，检查厂站模型
 返 回 值:    0-正常 其他-异常
***************************************************************/
int CTaskMngr::DoTaskManager(pthread_t ulMyTheadId)
{
    //m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()]");
    //检查重置断开front的厂站的设备通道，避免重复使能
    for(map<string,push* >::iterator it102=map102Push.begin();it102!=map102Push.end();it102++){
        lock(it102->second->lck_CloseUp);
        if(!it102->second->setUpCloseStn.empty()){
            it102->second->setUpCloseStn.clear();
            map<string,STN_CFG>::iterator itStn = mapStnLinkCfg.find(it102->first.c_str());
            if(itStn != mapStnLinkCfg.end()){
                //itStn->second.sApciPoiner.pStnFlow->ResetIedStep();
                //前置断开无需重置保护通道状态
            }else{
                //没找到，反正新增会关闭，就不管了
                m_pLogFile->FormatAdd(CLogFile::error,"[%s][CTaskMngr::DoTaskManager()]前置102断开，复位104设备链接 失败，找不到厂站",it102->first.c_str());
            }
        }
        unlock(it102->second->lck_CloseUp);
    }
    
    
    for(map<string,STN_CFG>::iterator itStn=mapStnLinkCfg.begin();itStn!=mapStnLinkCfg.end();itStn++){
        CLockUp lockUp(&itStn->second.sApciPoiner.pStnFlow->m_LockForCloseStn);
        if(!itStn->second.sApciPoiner.pStnFlow->setCloseStn.empty()){
            itStn->second.sApciPoiner.pStnFlow->setCloseStn.clear();
//            map<string,push* >::iterator it102=map102Push.find(itStn->first);
//            if(it102!=map102Push.end()){
//                lock(it102->second->lck_CloseDown);
//                it102->second->setDownCloseStn.insert(itStn->first);
//                m_pLogFile->FormatAdd(CLogFile::error,"[%s][CTaskMngr::DoTaskManager()]厂站104断开重连，断开前置102链接 setDownCloseStn[%d]",itStn->first.c_str(),
//                        it102->second->setDownCloseStn.size());
//                unlock(it102->second->lck_CloseDown);
//            }
        }
    }
    
    
    //按站分发sttp
    if(!m_RcvBusInfList.empty()){
        BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
        {
            CLockUp lockUp(&m_LockForRcvBusInfList);//锁，出括号自己释放
            Sttp_Info = m_RcvBusInfList.front();
            m_RcvBusInfList.pop_front();
        }
        int uMsgID = Sttp_Info.sttp_data.sttp_head.uMsgID;
        switch( uMsgID )
        {
        case 203://目录
        case 210://文件
        case 213://下装
            {
                string strStnId;
                strStnId = Sttp_Info.sttp_data.sttp_body.ch_pt_id;
                map<string,STN_CFG>::iterator itGet = mapStnLinkCfg.find(strStnId.c_str());
                if(itGet != mapStnLinkCfg.end()){
                    CLockUp lockUp(&itGet->second.sApciPoiner.pStnFlow->m_LockForRcvBusInfList);
                    itGet->second.sApciPoiner.pStnFlow->m_RcvBusInfList.push_back(Sttp_Info);
                    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] stn[%s]uMsgID[%d] down to stn Flow",itGet->first.c_str(),uMsgID);
                }else{
                    m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::DoTaskManager()] stn[%s]uMsgID[%d] down to stn Flow not Stn find,not my stn",strStnId.c_str(),uMsgID);
                }
            }
            break;
        case 20173://定值区1--测试BH99999 cpu1 zone1处理
            {
                string strPt = Sttp_Info.sttp_data.sttp_body.ch_pt_id;
                string strStn;
                if(IsStnMgr(strPt,strStn)==true){
                    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::DoTaskManager()]管理单元 20173 uMsgID[%d] rii[%d] pt[%s]cpu[%d]src[%d]zone[%d]nChangeConfigType[%d]",
                            strStn.c_str(),
                            Sttp_Info.sttp_data.sttp_head.uMsgID,Sttp_Info.sttp_data.sttp_head.uMsgRii,
                            Sttp_Info.sttp_data.sttp_body.ch_pt_id,Sttp_Info.sttp_data.sttp_body.nCpu,Sttp_Info.sttp_data.sttp_body.nSource,
                            Sttp_Info.sttp_data.sttp_body.nZone,Sttp_Info.sttp_data.sttp_body.nChangeConfigType);

                    STTP_FULL_DATA sttp_data20174;  
                    zero_sttp_full_data(sttp_data20174);
                    sttp_data20174.sttp_head.uMsgID = 20174;
                    sttp_data20174.sttp_head.uMsgType = 'I';
                    sttp_data20174.sttp_head.uMsgRii = Sttp_Info.sttp_data.sttp_head.uMsgRii;

                    snprintf(sttp_data20174.sttp_body.ch_pt_id,sizeof(sttp_data20174.sttp_body.ch_pt_id),"%s",Sttp_Info.sttp_data.sttp_body.ch_pt_id);
                    sttp_data20174.sttp_body.nCpu=1;
                    STTP_DATA s;
                    s.id=1;
                    s.str_value="1";
                    sttp_data20174.sttp_body.variant_member.value_data.push_back(s);
                    int r = _sendSttp2Msgbus(sttp_data20174);
                    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::DoTaskManager()]  MsgID[%d] up to bus ret[%d]",strStn.c_str(),sttp_data20174.sttp_head.uMsgID,r);
                }
            }
            break;
        case 20015://定值--测试BH99999 cpu1 zone1处理 mdl值
        case 20011://开关量--测试BH99999 cpu1 处理  通道状态，子站状态grp=1，,2
        case 20007://模拟量。desc="子站测量信息"--测试BH99999 grp=3
            {
                string strPt = Sttp_Info.sttp_data.sttp_body.ch_pt_id;
                string strStn;
                if(IsStnMgr(strPt,strStn)==true){
                    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()]stn[%s]管理单元 20011/20007/20015 uMsgID[%d] rii[%d] pt[%s]cpu[%d]src[%d]zone[%d]nChangeConfigType[%d]",
                            strStn.c_str(),
                            Sttp_Info.sttp_data.sttp_head.uMsgID,Sttp_Info.sttp_data.sttp_head.uMsgRii,
                            Sttp_Info.sttp_data.sttp_body.ch_pt_id,Sttp_Info.sttp_data.sttp_body.nCpu,Sttp_Info.sttp_data.sttp_body.nSource,
                            Sttp_Info.sttp_data.sttp_body.nZone,Sttp_Info.sttp_data.sttp_body.nChangeConfigType);
                    map<string,STN_CFG>::iterator itGet = mapStnLinkCfg.find(strStn.c_str());
                    if(itGet != mapStnLinkCfg.end()){
                        CLockUp lockUp(&itGet->second.sApciPoiner.pStnFlow->m_LockForRcvBusInfList);
                        itGet->second.sApciPoiner.pStnFlow->m_RcvBusInfList.push_back(Sttp_Info);
                        m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] stn[%s]管理单元uMsgID[%d] down to stn Flow",itGet->first.c_str(),uMsgID);
                    }else{
                        m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::DoTaskManager()] stn[%s]管理单元uMsgID[%d] down to stn Flow not Stn find,not my stn",strStn.c_str(),uMsgID);
                    }
                }
            }
            break;
        case 40003:
            {
                m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::DoTaskManager()]  40003 命令类型[%d]",Sttp_Info.sttp_data.sttp_body.nFlag);
                string strStnId;
                strStnId = Sttp_Info.sttp_data.sttp_body.ch_station_id;
                string strPtid;
                strPtid = Sttp_Info.sttp_data.sttp_body.ch_pt_id;

                map<string, STN_CFG>::iterator itGet = mapStnLinkCfg.find(strStnId);
                if (itGet != mapStnLinkCfg.end()) {
                    if (0 == Sttp_Info.sttp_data.sttp_body.nFlag) {
                        //配置生效
                        CLockUp lockUp(&itGet->second.sApciPoiner.pStnFlow->m_LockForRcvBusInfList);
                        itGet->second.sApciPoiner.pStnFlow->m_RcvBusInfList.push_back(Sttp_Info);
                        m_pLogFile->FormatAdd(CLogFile::trace, "[CTaskMngr::DoTaskManager()] stn[%s]uMsgID[%d]配置生效 down to stn Flow", itGet->first.c_str(), uMsgID);
                    }
                    else if (1 == Sttp_Info.sttp_data.sttp_body.nFlag) {
                        //关闭设备通道
                        int ok = itGet->second.sApciPoiner.pStnFlow->ResetOneIedStep(strPtid, Sttp_Info.sttp_data.sttp_head.uMsgRii);
                        itGet->second.sApciPoiner.pStnFlow->Send40004Sttp(strPtid, ok == 0 ? 0 : 1, Sttp_Info.sttp_data.sttp_head.uMsgRii);
                        m_pLogFile->FormatAdd(CLogFile::error, "[CTaskMngr::DoTaskManager()] stn[%s]uMsgID[%d]rii[%d]关闭通道 %s 回40004", itGet->first.c_str(),
                            uMsgID, Sttp_Info.sttp_data.sttp_head.uMsgRii, ok == 0 ? "成功" : "失败");

                        m_pLogFile->FormatAdd(CLogFile::trace, "[CTaskMngr::DoTaskManager()] stn[%s]uMsgID[%d]rii[%d]关闭通道40003 down to stn Flow", itGet->first.c_str(),
                            uMsgID, Sttp_Info.sttp_data.sttp_head.uMsgRii);
                    }
                    else if (2 == Sttp_Info.sttp_data.sttp_body.nFlag || 3 == Sttp_Info.sttp_data.sttp_body.nFlag) // 4003启停抓包命令
                    {
                        int nCmdType = 3;
                        if (3 == Sttp_Info.sttp_data.sttp_body.nFlag)nCmdType = 4;
                        itGet->second.sApciPoiner.pStnFlow->SendAsdu200TcCap(nCmdType, Sttp_Info.sttp_data.sttp_head.uMsgRii);
                        m_pLogFile->FormatAdd(CLogFile::error, "[CTaskMngr::DoTaskManager()] stn[%s] 收到来自前置40003 抓包命令[%d](3:启动,4：停止),Rii:%d", itGet->first.c_str(), nCmdType, Sttp_Info.sttp_data.sttp_head.uMsgRii);
                    }
                }
                else {
                    m_pLogFile->FormatAdd(CLogFile::error, "[CTaskMngr::DoTaskManager()] stn[%s]uMsgID[%d]rii[%d] down to stn Flow not Stn find,not my stn", strStnId.c_str(), uMsgID, Sttp_Info.sttp_data.sttp_head.uMsgRii);
                }

            }
            break;
        case 40005://招软件版本
        {
            if (Sttp_Info.sttp_data.sttp_body.nFlag == 1) {//1：装置软件版本
                string strStnId;
                strStnId = Sttp_Info.sttp_data.sttp_body.ch_station_id;
                map<string, STN_CFG>::iterator itGet = mapStnLinkCfg.find(strStnId.c_str());
                if (itGet != mapStnLinkCfg.end()) {
                    CLockUp lockUp(&itGet->second.sApciPoiner.pStnFlow->m_LockForRcvHnTcSttpList);
                    itGet->second.sApciPoiner.pStnFlow->m_RcvHnTcSttpList.push_back(Sttp_Info);
                    m_pLogFile->FormatAdd(CLogFile::trace, "[CTaskMngr::DoTaskManager()] stn[%s]uMsgID[%d] down to stn Flow", itGet->first.c_str(), uMsgID);
                }
                else {
                    m_pLogFile->FormatAdd(CLogFile::error, "[CTaskMngr::DoTaskManager()] stn[%s]uMsgID[%d]rii[%d] down to stn Flow not Stn find,not my stn", strStnId.c_str(), uMsgID, Sttp_Info.sttp_data.sttp_head.uMsgRii);
                }
            }
        }
        break;
        case 20165://刷新pt和ied那么关系
        {
            if (Sttp_Info.sttp_data.sttp_body.nFlag == 1) {//0-卸载 1-加载
                string strStnId;
                strStnId = Sttp_Info.sttp_data.sttp_body.ch_station_id;
                map<string, STN_CFG>::iterator itGet = mapStnLinkCfg.find(strStnId.c_str());
                if (itGet != mapStnLinkCfg.end()) {
                    CLockUp lockUp(&itGet->second.sApciPoiner.pStnFlow->m_LockForRcvHnTcSttpList);
                    itGet->second.sApciPoiner.pStnFlow->m_RcvHnTcSttpList.push_back(Sttp_Info);
                    m_pLogFile->FormatAdd(CLogFile::trace, "[CTaskMngr::DoTaskManager()] stn[%s]uMsgID[%d] down to stn Flow", itGet->first.c_str(), uMsgID);
                }
                else {
                    //太多不打印
                }
            }
        }
        break;
        break;
        default:
            m_pLogFile->FormatAdd(CLogFile::trace, "[CTaskMngr::DoTaskManager()]down [%d]uMsgID not care", uMsgID);
            break;
        }
    }

    //检查有无上送sttp,MMS向front
    for(map<string,STN_CFG>::iterator itStn=mapStnLinkCfg.begin();itStn!=mapStnLinkCfg.end();itStn++){
        //sttp
        bool bEmpty;
        {
            CLockUp lockUp(&itStn->second.sApciPoiner.pStnFlow->m_LockForSendBusList);//锁，出括号自己释放
            bEmpty = itStn->second.sApciPoiner.pStnFlow->m_SendBusList.empty();
        }
        //printf("---jianglei %p %s\n", itStn->second.sApciPoiner.pStnFlow, itStn->second.sApciPoiner.pStnFlow->m_bConnected ? "TRUE" : "FALSE");
        if (!bEmpty && m_nLinkStatus == 1) {
            BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
            {
                CLockUp lockUp(&itStn->second.sApciPoiner.pStnFlow->m_LockForSendBusList);//锁，出括号自己释放
                Sttp_Info = itStn->second.sApciPoiner.pStnFlow->m_SendBusList.front();
                itStn->second.sApciPoiner.pStnFlow->m_SendBusList.pop_front();

                //m_pLogFile->FormatAdd(CLogFile::trace, "[CTaskMngr::DoTaskManager()] stn[%s]-----jianglei----[%s]-----", itStn->first.c_str(), itStn->second.sApciPoiner.pStnFlow->m_bConnected?"TRUE":"FALSE");

                if (!itStn->second.sApciPoiner.pStnFlow->m_bConnected)
                {
                    if (time(NULL) - Sttp_Info.t_time < itStn->second.nStnConnectChgSec)
                    {
                        itStn->second.sApciPoiner.pStnFlow->m_SendBusList.push_back(Sttp_Info);
                        continue;
                    }
                }
                if (itStn->second.sApciPoiner.pStnFlow->m_bConnected)
                {
                    if (time(NULL) - Sttp_Info.t_time >= itStn->second.nStnConnectChgSec)
                    {
                        continue;
                    }
                }              
            }
                int r = _sendSttp2Msgbus(Sttp_Info.sttp_data);
                m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] stn[%s]uMsgID[%d] up to bus ret[%d]",itStn->first.c_str(),Sttp_Info.sttp_data.sttp_head.uMsgID,r);
                if(Sttp_Info.sttp_data.sttp_head.uMsgID == 215){
                    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] stn[%s]  uMsgID[%d] pt[%s] strFileName[%s] nOffset[%d] nSize[%d] nFileNO[%d]",itStn->first.c_str(),
                            Sttp_Info.sttp_data.sttp_head.uMsgID,
                            Sttp_Info.sttp_data.sttp_body.ch_pt_id,
                            Sttp_Info.sttp_data.sttp_body.variant_member.file_data.strFileName.c_str(),
                            Sttp_Info.sttp_data.sttp_body.variant_member.file_data.nOffset,
                            Sttp_Info.sttp_data.sttp_body.variant_member.file_data.nSize,
                            Sttp_Info.sttp_data.sttp_body.variant_member.file_data.nFileNO);
                }else
                if(Sttp_Info.sttp_data.sttp_head.uMsgID == 20157){
                    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] stn[%s]  uMsgID[%d] pt[%s] nMsgId[%d] strMessage[%s]",itStn->first.c_str(),
                            Sttp_Info.sttp_data.sttp_head.uMsgID,
                            Sttp_Info.sttp_data.sttp_body.ch_pt_id,
                            Sttp_Info.sttp_data.sttp_body.nMsgId,
                            Sttp_Info.sttp_data.sttp_body.strMessage.c_str());
                }else
                if(Sttp_Info.sttp_data.sttp_head.uMsgID == 40004){
                    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] stn[%s] rii[%d] uMsgID[%d] stn[%s] nStatus[%d]",itStn->first.c_str(),
                            Sttp_Info.sttp_data.sttp_head.uMsgRii,
                            Sttp_Info.sttp_data.sttp_head.uMsgID,
                            Sttp_Info.sttp_data.sttp_body.ch_station_id,
                            Sttp_Info.sttp_data.sttp_body.nStatus);
                }else
                if(Sttp_Info.sttp_data.sttp_head.uMsgID == 20144){
                    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] stn[%s] rii[%d] uMsgID[%d] stn[%s] nStatus[%d][(0-断开 1-正常)] [%d][1-1区, 2-2区，3-3区]",itStn->first.c_str(),
                            Sttp_Info.sttp_data.sttp_head.uMsgRii,
                            Sttp_Info.sttp_data.sttp_head.uMsgID,
                            Sttp_Info.sttp_data.sttp_body.ch_station_id,
                            Sttp_Info.sttp_data.sttp_body.nStatus,
                            Sttp_Info.sttp_data.sttp_body.nFlag);
                }else
                if(Sttp_Info.sttp_data.sttp_head.uMsgID == 20002){
                    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] 20002 stn[%s] rii[%d] uMsgID[%d] value_data[%d]",itStn->first.c_str(),
                            Sttp_Info.sttp_data.sttp_head.uMsgRii,
                            Sttp_Info.sttp_data.sttp_head.uMsgID,
                            Sttp_Info.sttp_data.sttp_body.variant_member.value_data.size());
                    for(vector<STTP_DATA>::iterator it1=Sttp_Info.sttp_data.sttp_body.variant_member.value_data.begin();it1!=Sttp_Info.sttp_data.sttp_body.variant_member.value_data.end();it1++){
                        m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] 20002 stn[%s] pt[%s]id[%d][0-正常，1-停运,2-未知]",itStn->first.c_str(),
                            it1->str_value.c_str(),it1->id);
                    }
                }else
                if(Sttp_Info.sttp_data.sttp_head.uMsgID == 20010){
                    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] 20010 stn[%s] rii[%d] uMsgID[%d] value_data[%d]",itStn->first.c_str(),
                            Sttp_Info.sttp_data.sttp_head.uMsgRii,
                            Sttp_Info.sttp_data.sttp_head.uMsgID,
                            Sttp_Info.sttp_data.sttp_body.variant_member.value_data.size());
                    for(vector<STTP_DATA>::iterator it1=Sttp_Info.sttp_data.sttp_body.variant_member.value_data.begin();it1!=Sttp_Info.sttp_data.sttp_body.variant_member.value_data.end();it1++){
                        m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] 20010 stn[%s] di-id[%d]val[%s][0-off，1-on]",itStn->first.c_str(),
                            it1->id,it1->str_value.c_str());
                    }
                }else
                if(Sttp_Info.sttp_data.sttp_head.uMsgID == 20006){
                    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] 20006 stn[%s] rii[%d] uMsgID[%d] value_data[%d]",itStn->first.c_str(),
                            Sttp_Info.sttp_data.sttp_head.uMsgRii,
                            Sttp_Info.sttp_data.sttp_head.uMsgID,
                            Sttp_Info.sttp_data.sttp_body.variant_member.value_data.size());
                    for(vector<STTP_DATA>::iterator it1=Sttp_Info.sttp_data.sttp_body.variant_member.value_data.begin();it1!=Sttp_Info.sttp_data.sttp_body.variant_member.value_data.end();it1++){
                        m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] 20006 stn[%s] ana-id[%d]val[%s][模拟量采样值]",itStn->first.c_str(),
                            it1->id,it1->str_value.c_str());
                    }
                }else
                if(Sttp_Info.sttp_data.sttp_head.uMsgID == 20016){
                    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] 20016 stn[%s] rii[%d] uMsgID[%d] ch_pt_id[%s] nZone[%d]nCpu[%d]nSource[%d]value_data[%d]",itStn->first.c_str(),
                            Sttp_Info.sttp_data.sttp_head.uMsgRii,
                            Sttp_Info.sttp_data.sttp_head.uMsgID,
                            Sttp_Info.sttp_data.sttp_body.ch_pt_id,
                            Sttp_Info.sttp_data.sttp_body.nZone,
                            Sttp_Info.sttp_data.sttp_body.nCpu,
                            Sttp_Info.sttp_data.sttp_body.nSource,
                            Sttp_Info.sttp_data.sttp_body.variant_member.value_data.size());
                    for(vector<STTP_DATA>::iterator it1=Sttp_Info.sttp_data.sttp_body.variant_member.value_data.begin();it1!=Sttp_Info.sttp_data.sttp_body.variant_member.value_data.end();it1++){
                        m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] 20016 stn[%s] 定值-id[%d]val[%s][BH99999定值]",itStn->first.c_str(),
                            it1->id,it1->str_value.c_str());
                    }
                }else
                if(Sttp_Info.sttp_data.sttp_head.uMsgID == 20012){
                    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] 20012 stn[%s] rii[%d] uMsgID[%d] ch_pt_id[%s]nCpu[%d]time[%s] value_data[%d]",itStn->first.c_str(),
                            Sttp_Info.sttp_data.sttp_head.uMsgRii,
                            Sttp_Info.sttp_data.sttp_head.uMsgID,
                            Sttp_Info.sttp_data.sttp_body.ch_pt_id,
                            Sttp_Info.sttp_data.sttp_body.nCpu,
                            Sttp_Info.sttp_data.sttp_body.ch_time_15_BIT1,
                            Sttp_Info.sttp_data.sttp_body.variant_member.value_data.size());
                    for(vector<STTP_DATA>::iterator it1=Sttp_Info.sttp_data.sttp_body.variant_member.value_data.begin();it1!=Sttp_Info.sttp_data.sttp_body.variant_member.value_data.end();it1++){
                        m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] 20012 stn[%s] 回复di-id[%d]val[%s][0-off，1-on]",itStn->first.c_str(),
                            it1->id,it1->str_value.c_str());
                    }
                }else
                if(Sttp_Info.sttp_data.sttp_head.uMsgID == 20008){
                    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] 20008 stn[%s] rii[%d] uMsgID[%d] ch_pt_id[%s]nCpu[%d]time[%s] value_data[%d]",itStn->first.c_str(),
                            Sttp_Info.sttp_data.sttp_head.uMsgRii,
                            Sttp_Info.sttp_data.sttp_head.uMsgID,
                            Sttp_Info.sttp_data.sttp_body.ch_pt_id,
                            Sttp_Info.sttp_data.sttp_body.nCpu,
                            Sttp_Info.sttp_data.sttp_body.ch_time_15_BIT1,
                            Sttp_Info.sttp_data.sttp_body.variant_member.value_data.size());
                    for(vector<STTP_DATA>::iterator it1=Sttp_Info.sttp_data.sttp_body.variant_member.value_data.begin();it1!=Sttp_Info.sttp_data.sttp_body.variant_member.value_data.end();it1++){
                        m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] 20008 stn[%s] 回复ana-id[%d]val[%s]",itStn->first.c_str(),
                            it1->id,it1->str_value.c_str());
                    }
                }else
                if(Sttp_Info.sttp_data.sttp_head.uMsgID == 40006){
                    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()] stn[%s] rii[%d] uMsgID[%d] stn[%s] pt_id[%s] HandlerName[%s] nFlag[%d] ch_version[%s] nStatus[%d] Message[%s]",itStn->first.c_str(),
                            Sttp_Info.sttp_data.sttp_head.uMsgRii,
                            Sttp_Info.sttp_data.sttp_head.uMsgID,
                            Sttp_Info.sttp_data.sttp_body.ch_station_id,
                            Sttp_Info.sttp_data.sttp_body.ch_pt_id,
                            Sttp_Info.sttp_data.sttp_body.ch_HandlerName,
                            Sttp_Info.sttp_data.sttp_body.nFlag,
                            Sttp_Info.sttp_data.sttp_body.ch_version,
                            Sttp_Info.sttp_data.sttp_body.nStatus,
                            Sttp_Info.sttp_data.sttp_body.strMessage.c_str());
                }
            //}
        }
        //MMS--dev回
        map<string,push* >::iterator it102 = map102Push.find(itStn->first);
        if(it102 != map102Push.end()){
            CLockUp lockUp(&itStn->second.sApciPoiner.pStnFlow->m_LockForSendHnTcMsgList);
            for(map<int,CHNO_TC_MSG >::iterator itCh = itStn->second.sApciPoiner.pStnFlow->m_mapNoTcMsg.begin();itCh != itStn->second.sApciPoiner.pStnFlow->m_mapNoTcMsg.end();itCh++){
                //检查每个通道是否有完整的tpkt，有则发一条结束，后续下次进来再发，保证一个tpkt一条tcp基本。
                if(itCh->second.vFullMsg.empty()){// && itCh->second.vHugeMsg.empty()
                    continue;
                }
                if (itCh->second.vFullMsg.at(0) != RFC1006_VERSION){
                    m_pLogFile->FormatAdd(CLogFile::error,"[%s][%s][CTaskMngr::DoTaskManager()]通道[%d] MMS msglen[%d] Not Tpkt version 3 ", \
                        itStn->first.c_str(),itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),
                            itCh->first,itCh->second.vFullMsg.size() );
                    //删除错误数据？可能导致后面也错乱？应该关闭conn感觉
                    //itCh->second.erase(itCh->second.begin(),itCh->second.end());
                    PrintBytes(itCh->second.vFullMsg);
                    continue;
                }
                if(itCh->second.vFullMsg.size()<4){
                    m_pLogFile->FormatAdd(CLogFile::error,"[%s][CTaskMngr::DoTaskManager()]通道[%d] MMS msglen[%d]<4 ",itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),
                            itCh->first,itCh->second.vFullMsg.size() );
                    continue;
                }
                
                int tpkt_len=0;//从tpkt03头开始到尾
                tpkt_len = ( ( (unsigned short) itCh->second.vFullMsg.at(2) ) << 8 ) | (unsigned char)itCh->second.vFullMsg.at(3);
                if(itCh->second.vFullMsg.size()>=tpkt_len){
                    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::DoTaskManager()]通道[%d] MMS msglen[%d] has full  tpkt [%d]",itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),
                            itCh->first,itCh->second.vFullMsg.size(),tpkt_len );
                }else{
                    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::DoTaskManager()]通道[%d] MMS msglen[%d] 可能还没收全  tpkt应有长度[%d]",itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),
                            itCh->first,itCh->second.vFullMsg.size(),tpkt_len );
                    continue;
                }
                
                //有一个完整tpkt了，检查是否有后续,无则发，有则另存
                string strOneTpkt;
                strOneTpkt.assign(itCh->second.vFullMsg.begin(), itCh->second.vFullMsg.begin()+tpkt_len);
                
                m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::DoTaskManager()]time  通道号[%d] 有mms发往102口,内容如下",itCh->first);
                PrintBytes(strOneTpkt);
                
                bool bFront = true;
                
                int nCanTc = HasLastCOTP(itStn->second.sApciPoiner.pStnFlow->m_strStnId,itCh->first,strOneTpkt);//0-不是last要另存等全了判定转发，1-是Last可以透传,-1-判断失败
                if(nCanTc==1){
                    //1-是Last可以透传
                    if(itCh->second.listHugeMsg.empty()){//小mms直接传  //16 bits (min=7, max=65535).肯定小于
                        if(tpkt_len < 1024*1024){
                            //检查单tpkt是否invokeid为0，为0的是我发的召唤版本信息的包
                            if(sCtlKeyCfg.nUseMMS==1){
                                Pack_TPKT vByteOneTpkt;
                                list_TPKT listTokt;
                                vByteOneTpkt.assign(strOneTpkt.begin(),strOneTpkt.end());
                                listTokt.push_back(vByteOneTpkt);
                                unsigned int nInvokeID = -1;
                                bool bMMsOk = itStn->second.sApciPoiner.pStnFlow->m_pCMMSAnalyze->Analyze_InvokeID(listTokt,nInvokeID);
                                if(bMMsOk){
                                    if(0==nInvokeID){
                                        //删除已另存的
                                        itCh->second.vFullMsg.erase(itCh->second.vFullMsg.begin(),itCh->second.vFullMsg.begin()+tpkt_len);
                                        //不允许透传
                                        bFront = false;
                                        m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::DoTaskManager()] 通道号[%d] 有nInvokeID[%d]=0 的crc版本回复 短包,开始解析内容,不往前置转发",
                                                itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),itCh->first,nInvokeID);

                                        Analyze_Read_Respond sResult;
                                        itStn->second.sApciPoiner.pStnFlow->m_pCMMSAnalyze->Analyze_ReadRespond(listTokt,sResult);
                                        if(sResult.accessresult){
                                            if(sResult.resultlst.size()==1){
                                                vector<Read_Result>::iterator itCrcVal = sResult.resultlst.begin();//只有一条
                                                if(itCrcVal != sResult.resultlst.end()){
                                                    vector<string>::iterator itVal = (*itCrcVal).lst_Value.begin();
                                                    if(itVal != (*itCrcVal).lst_Value.end()){
                                                        string strMsg = itVal->c_str();
                                                        if(strMsg.empty()){
                                                            m_pLogFile->FormatAdd(CLogFile::error,"[%s][CTaskMngr::DoTaskManager()] 通道号[%d] 有nInvokeID[%d]=0 的crc版本回复 短包,解析内容成功,版本值[%s]为空,发40006",
                                                                        itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),itCh->first,nInvokeID,strMsg.c_str());
                                                            string strMsg="返回值为空";
                                                            itStn->second.sApciPoiner.pStnFlow->Send40006Result(itCh->first,0,strMsg);
                                                        }else{
                                                            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::DoTaskManager()] 通道号[%d] 有nInvokeID[%d]=0 的crc版本回复 短包,解析内容成功,版本值[%s],发40006",
                                                                        itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),itCh->first,nInvokeID,strMsg.c_str());
                                                            itStn->second.sApciPoiner.pStnFlow->Send40006Result(itCh->first,1,strMsg);
                                                        }
                                                    }else{
                                                        m_pLogFile->FormatAdd(CLogFile::error,"[%s][CTaskMngr::DoTaskManager()] 通道号[%d] 有nInvokeID[%d]=0 的crc版本回复 短包,解析内容成功,accessresult[%d],resultlst[%d]lst_Value[%d]空异常",
                                                            itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),itCh->first,nInvokeID,sResult.accessresult,sResult.resultlst.size(),(*itCrcVal).lst_Value.size());  
                                                        string strMsg="mms值list异常空";
                                                        itStn->second.sApciPoiner.pStnFlow->Send40006Result(itCh->first,0,strMsg);
                                                    }
                                                }else{
                                                   m_pLogFile->FormatAdd(CLogFile::error,"[%s][CTaskMngr::DoTaskManager()] 通道号[%d] 有nInvokeID[%d]=0 的crc版本回复 短包,解析内容成功,accessresult[%d],resultlst[%d]空,异常",
                                                         itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),itCh->first,nInvokeID,sResult.accessresult,sResult.resultlst.size());  
                                                   string strMsg="版本回复包异常空";
                                                   itStn->second.sApciPoiner.pStnFlow->Send40006Result(itCh->first,0,strMsg);
                                                }
                                            }else{
                                                m_pLogFile->FormatAdd(CLogFile::error,"[%s][CTaskMngr::DoTaskManager()]time  通道号[%d] 有nInvokeID[%d]=0 的crc版本回复 短包,解析内容成功,accessresult[%d],resultlst[%d]!=1 大小异常",
                                                    itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),itCh->first,nInvokeID,sResult.accessresult,sResult.resultlst.size()); 
                                                string strMsg="版本回复包异常大小";
                                                itStn->second.sApciPoiner.pStnFlow->Send40006Result(itCh->first,0,strMsg);
                                            }
                                        }else{
                                            m_pLogFile->FormatAdd(CLogFile::error,"[%s][CTaskMngr::DoTaskManager()] 通道号[%d] 有nInvokeID[%d]=0 的crc版本回复 短包,解析内容失败,accessresult[%d]",
                                                itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),itCh->first,nInvokeID,sResult.accessresult);
                                            string strMsg="版本回复包解析失败";
                                            itStn->second.sApciPoiner.pStnFlow->Send40006Result(itCh->first,0,strMsg);
                                        }
                                    }
                                }
                            }

                            if(bFront){
                                //删除已发
                                itCh->second.vFullMsg.erase(itCh->second.vFullMsg.begin(),itCh->second.vFullMsg.begin()+tpkt_len);
                                //透传给前置
                                lock(it102->second->lck_send);
                                it102->second->listSendMsg.push_back(strOneTpkt);
                                unlock(it102->second->lck_send);
                                m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::DoTaskManager()] MMS  tpkt msglen[%d]  send up to ZcsFront 流vector剩余大小[%d]wnonblock-ret[%d] time",
                                    itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),tpkt_len,itCh->second.vFullMsg.size(),0);
                                PrintBytes(strOneTpkt);
                                break;//发完一条即可，下次线程周期继续。
                            }
                        }else{
                            m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::DoTaskManager()] MMS stn[%s] msg size[%d] > 1024000,流vector剩余大小[%d]",itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),
                                    tpkt_len,itCh->second.vFullMsg.size());
                        }
                    }else{//有前面的长mms
                        //检查单tpkt是否invokeid为0，为0的是我发的召唤版本信息的包
                        if(sCtlKeyCfg.nUseMMS==1){
                            list_TPKT listTokt;
                            for(list<string>::iterator itH=itCh->second.listHugeMsg.begin();itH!=itCh->second.listHugeMsg.end();itH++){
                                Pack_TPKT vByteOneTpkt;
                                vByteOneTpkt.assign(itH->begin(),itH->end());
                                listTokt.push_back(vByteOneTpkt);
                            }
                            Pack_TPKT vByteOneTpkt;
                            vByteOneTpkt.assign(strOneTpkt.begin(),strOneTpkt.end());
                            listTokt.push_back(vByteOneTpkt);

                            unsigned int nInvokeID = -1;
                            bool bMMsOk = itStn->second.sApciPoiner.pStnFlow->m_pCMMSAnalyze->Analyze_InvokeID(listTokt,nInvokeID);
                            if(bMMsOk){
                                if(0==nInvokeID){
                                    //删除已另存的
                                    itCh->second.listHugeMsg.clear();
                                    itCh->second.vFullMsg.erase(itCh->second.vFullMsg.begin(),itCh->second.vFullMsg.begin()+tpkt_len);
                                    //不允许透传
                                    bFront = false;
                                    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::DoTaskManager()] 通道号[%d] 有nInvokeID[%d]=0 的crc版本回复 短包,开始解析内容,不往前置转发",
                                            itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),itCh->first,nInvokeID);

                                    Analyze_Read_Respond sResult;
                                    itStn->second.sApciPoiner.pStnFlow->m_pCMMSAnalyze->Analyze_ReadRespond(listTokt,sResult);
                                    if(sResult.accessresult){
                                        if(sResult.resultlst.size()==1){
                                            vector<Read_Result>::iterator itCrcVal = sResult.resultlst.begin();//只有一条
                                            if(itCrcVal != sResult.resultlst.end()){
                                                vector<string>::iterator itVal = (*itCrcVal).lst_Value.begin();
                                                if(itVal != (*itCrcVal).lst_Value.end()){
                                                    string strMsg = itVal->c_str();
                                                    if(strMsg.empty()){
                                                        m_pLogFile->FormatAdd(CLogFile::error,"[%s][CTaskMngr::DoTaskManager()] 通道号[%d] 有nInvokeID[%d]=0 的crc版本回复 短包,解析内容成功,版本值[%s]为空,发40006",
                                                                    itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),itCh->first,nInvokeID,strMsg.c_str());
                                                        string strMsg="返回值为空";
                                                        itStn->second.sApciPoiner.pStnFlow->Send40006Result(itCh->first,0,strMsg);
                                                    }else{
                                                        m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::DoTaskManager()] 通道号[%d] 有nInvokeID[%d]=0 的crc版本回复 短包,解析内容成功,版本值[%s],发40006",
                                                                    itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),itCh->first,nInvokeID,strMsg.c_str());
                                                        itStn->second.sApciPoiner.pStnFlow->Send40006Result(itCh->first,1,strMsg);
                                                    }
                                                }else{
                                                    m_pLogFile->FormatAdd(CLogFile::error,"[%s][CTaskMngr::DoTaskManager()] 通道号[%d] 有nInvokeID[%d]=0 的crc版本回复 短包,解析内容成功,accessresult[%d],resultlst[%d]lst_Value[%d]空异常",
                                                        itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),itCh->first,nInvokeID,sResult.accessresult,sResult.resultlst.size(),(*itCrcVal).lst_Value.size());  
                                                    string strMsg="mms值list异常空";
                                                    itStn->second.sApciPoiner.pStnFlow->Send40006Result(itCh->first,0,strMsg);
                                                }
                                            }else{
                                               m_pLogFile->FormatAdd(CLogFile::error,"[%s][CTaskMngr::DoTaskManager()] 通道号[%d] 有nInvokeID[%d]=0 的crc版本回复 短包,解析内容成功,accessresult[%d],resultlst[%d]空,异常",
                                                     itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),itCh->first,nInvokeID,sResult.accessresult,sResult.resultlst.size());  
                                               string strMsg="版本回复包异常空";
                                               itStn->second.sApciPoiner.pStnFlow->Send40006Result(itCh->first,0,strMsg);
                                            }
                                        }else{
                                            m_pLogFile->FormatAdd(CLogFile::error,"[%s][CTaskMngr::DoTaskManager()]time  通道号[%d] 有nInvokeID[%d]=0 的crc版本回复 短包,解析内容成功,accessresult[%d],resultlst[%d]!=1 大小异常",
                                                itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),itCh->first,nInvokeID,sResult.accessresult,sResult.resultlst.size()); 
                                            string strMsg="版本回复包异常大小";
                                            itStn->second.sApciPoiner.pStnFlow->Send40006Result(itCh->first,0,strMsg);
                                        }
                                    }else{
                                        m_pLogFile->FormatAdd(CLogFile::error,"[%s][CTaskMngr::DoTaskManager()] 通道号[%d] 有nInvokeID[%d]=0 的crc版本回复 短包,解析内容失败,accessresult[%d]",
                                            itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),itCh->first,nInvokeID,sResult.accessresult);
                                        string strMsg="版本回复包解析失败";
                                        itStn->second.sApciPoiner.pStnFlow->Send40006Result(itCh->first,0,strMsg);
                                    }

                                }
                            }
                        }


                        if(bFront){
                            //有前面的长mms
                            int nSize=itCh->second.listHugeMsg.size();
                            for(int i=0;i<nSize;i++){
                                string strMsgHug = itCh->second.listHugeMsg.front();
                                itCh->second.listHugeMsg.pop_front();

                                lock(it102->second->lck_send);
                                it102->second->listSendMsg.push_back(strMsgHug);
                                unlock(it102->second->lck_send);
                                m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::DoTaskManager()] MMS 大包发送一条 tpkt msglen[%d]  send up to ZcsFront wnonblock-ret[%d]",
                                    itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),strMsgHug.size(),0);
                            }
                            itCh->second.listHugeMsg.clear();
                            //发last tpkt
                            itCh->second.vFullMsg.erase(itCh->second.vFullMsg.begin(),itCh->second.vFullMsg.begin()+tpkt_len);

                            lock(it102->second->lck_send);
                            it102->second->listSendMsg.push_back(strOneTpkt);
                            unlock(it102->second->lck_send);
                            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::DoTaskManager()] MMS 大包发送 最后一条 tpkt msglen[%d]  send up to ZcsFront 流vector剩余大小[%d]wnonblock-ret[%d]",
                                itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),tpkt_len,itCh->second.vFullMsg.size(),0);
                            break;//发完一整条即可，下次线程周期继续。
                        }
                    }
                }else if(nCanTc==0){
                    //0-不是last要另存等全了判定转发
                    itCh->second.listHugeMsg.push_back(strOneTpkt);
                    //删除已另存的
                    itCh->second.vFullMsg.erase(itCh->second.vFullMsg.begin(),itCh->second.vFullMsg.begin()+tpkt_len);
                    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::DoTaskManager()] MMS  tpkt msglen[%d]  非lastCOTP，缓存等待全COPT了再 send up to ZcsFront,已缓存[%d]条 流vector剩余大小[%d]",
                            itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str(),tpkt_len,itCh->second.listHugeMsg.size(),itCh->second.vFullMsg.size());
                    PrintBytes(strOneTpkt);
                }else{
					m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::DoTaskManager()] 删除本帧报文，不转发",
						itStn->second.sApciPoiner.pStnFlow->m_strStnId.c_str());
					//发last tpkt
					itCh->second.vFullMsg.erase(itCh->second.vFullMsg.begin(),itCh->second.vFullMsg.begin()+tpkt_len);
                }
            }           
        }
    }
    
    //按站分发MMS向子站向设备
    for(map<string,push* >::iterator it102=map102Push.begin();it102!=map102Push.end();it102++){
        lock(it102->second->lck_msg);
        if(!it102->second->vMsg.empty()){
            string oneMsg = it102->second->vMsg.front();
            it102->second->vMsg.pop_front();
            PrintBytes(oneMsg);
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::DoTaskManager()] --------stn 向子站向设备 vmsg 剩余条数[%d]",it102->first.c_str(),it102->second->vMsg.size());
            map<string,STN_CFG>::iterator itGet = mapStnLinkCfg.find(it102->first);
            if(itGet == mapStnLinkCfg.end()){
                m_pLogFile->FormatAdd(CLogFile::error,"[%s] [CTaskMngr::DoTaskManager()] MMS 子站stn not find flow,clear msg size[%d]=>0 关闭前置链接,wait next loop",
                        it102->first.c_str(),it102->second->vMsg.size());
                it102->second->vMsg.clear();//清理

                //lock(it102->second->lck_CloseDown);
                //it102->second->setDownCloseStn.insert(it102->first);
                //unlock(it102->second->lck_CloseDown);
            }else{
                CLockUp lockUp(&itGet->second.sApciPoiner.pStnFlow->m_LockForRcvHnTcMsgList);
                itGet->second.sApciPoiner.pStnFlow->m_RcvHnTcMsgList.push_back(oneMsg);
                m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::DoTaskManager()] MMS Msglist msglen[%d] down to stn Flow------------------",itGet->first.c_str(),oneMsg.length());
            }
        }
        unlock(it102->second->lck_msg);
    }
    
    //61850前置连接成功发送20002处理
    HandleFrontLinkChange();
    return 0;
}
int CTaskMngr::init_msg_center_client()
{
	int nRet = 0;

	try
	{
		m_pLoadBusSwapLib = new CZXLoadBusSwapLib(m_pLogFile);//update 20170523 换为成员指针m_pLogFile
		//加载消息中心客户端动态库
		bool bRet = m_pLoadBusSwapLib->LoadShareLib();
		if (!bRet)
		{
			m_pLogFile->Add("加载消息中心客户端动态库失败!",CLogFile::error);
			return 1;
		}
		//创建消息中心客户端实例
		char cTmp[FILE_NAME_MAX_LEN];
		bzero(cTmp, FILE_NAME_MAX_LEN);
		sprintf(cTmp,"%s/SttpBusSwap_Log/",m_LogCfg.strLogPath.c_str()); 
		m_pIZxBusSwap = m_pLoadBusSwapLib->CreateBusIns(m_pLogFile, cTmp);//update 20170523 g_logfile换为成员指针m_pLogFile
		//update by xdy 20170407 修正创建消息中心客户端实例的传入参数，即增加总线日志路径参数 end

		if (NULL == m_pIZxBusSwap)
		{
			m_pLogFile->Add("[init_msg_center_client]CreateBusIns 失败!",CLogFile::error);
			return 1;
		}
		m_pLogFile->Add("[init_msg_center_client]CreateBusIns 成功!",CLogFile::trace);

		//填充结构体
		APP_NODE_INFO node ;
		_fill_APP_NODE_INFO(node);
		//初始化登录
		if( m_pIZxBusSwap->InitAppNode(node) < 0 )
		{
			m_pLogFile->Add("[init_msg_center_client]InitAppNode 失败!",CLogFile::error);
			return 1; 
		}
		m_pLogFile->Add("[init_msg_center_client]InitAppNode 成功!",CLogFile::trace);

		// 注册回调
        nRet = m_pIZxBusSwap->RegRecvSttpMsgCallBak(this, _bus_swap_callback);
		if (nRet != 0)
		{
			m_pLogFile->Add("[init_msg_center_client]RegRecvSttpMsgCallBak 失败!",CLogFile::error);
		}
		else
		{
			m_pLogFile->Add("[init_msg_center_client]RegRecvSttpMsgCallBak 成功!",CLogFile::trace);
		}
        nRet = m_pIZxBusSwap->RegConntStatusChgCallBak(this, _bus_conn_callback);
        if (nRet != 0)
        {
            m_pLogFile->Add("[init_msg_center_client]RegConntStatusChgCallBak 失败!", CLogFile::error);
        }
        else
        {
            m_pLogFile->Add("[init_msg_center_client]RegConntStatusChgCallBak 成功!", CLogFile::trace);
        }
		// 启动
		nRet = m_pIZxBusSwap->StartBusSwap();
		if (nRet != 0)
		{
			m_pLogFile->Add("[init_msg_center_client]StartBusSwap 失败!",CLogFile::error);
		}
		else
		{
			m_pLogFile->Add("[init_msg_center_client]StartBusSwap 成功!",CLogFile::trace);
		}
	}
	catch(...)
	{
		m_pLogFile->Add("CZxDataHandler::init_msg_center_client出现异常!",CLogFile::error);
		nRet = 1;
	}

	return nRet;
}


int CTaskMngr::_fill_APP_NODE_INFO(APP_NODE_INFO &node)
{
	int nRet = 0;
	CXJString strLog;
	//node.nProcType = STTPNET_MODEL_INFOOUTPUT;	
	node.nProcType = 130;		    //应用进程模块编号 (见ZxSttpDefine.h中模块定义)
	node.strProcName = EXE_NAME;//应用进程名称（不要超过24字节,如果同一个进程需注册多个节点,则进程名需不同)

//	node.strContextName = "realtime";       //态名(d5000总线有效,默认"realtime",具体见常量定义)
//	node.strAppName = "scada_relay";           //应用名(d5000总线有效,默认"scada_relay",具体见常量定义)

	//订阅的消息通道列表(d5000有效,范围0-255,一般应用只需订阅一个通道,支持订阅多个通道)//up=250
	char ctmp[255];
	memset(ctmp, 0, sizeof(ctmp));
	GetIniKey(ZX_BUS_DEF_INI, "BUS_CHANNEL", "up", ctmp);
	short uRecvchnl = atoi(ctmp);
	m_uSendchnl_up = uRecvchnl;//对页面的通道号
    node.vRcvMsgChannelList.push_back(uRecvchnl);
	node.nSndMsgChannel = uRecvchnl;
	strLog.Format("发送通道1: %d", uRecvchnl);
	m_pLogFile->Add((char *)strLog.c_str(),CLogFile::trace);

	GetIniKey(ZX_BUS_DEF_INI, "BUS_CHANNEL", "down", ctmp);
	short uRecvchn2 = atoi(ctmp);
	m_uSendchnl_down = uRecvchn2;//对页面的通道号
	node.vRcvMsgChannelList.push_back(uRecvchn2);
	strLog.Format( "接收通道1: %d", uRecvchn2);
	m_pLogFile->Add((char *)strLog.c_str(),CLogFile::trace);
    
	//发送消息的通道(事件集)(d5000有效,范围0-255,具体见常量定义)//down=231
	
    
	//response=624 event=625
	////订阅消息事件主题列表(d5000中范围0-1300,一般为命令、结果及事件通知三类,具体见常量定义)
	memset(ctmp, 0, sizeof(ctmp));
	GetIniKey(ZX_BUS_DEF_INI, "BUS_EVENT", "response", ctmp);
	short ntmp = atoi(ctmp);
	m_RespCode = ntmp;
	node.vEventTopicList.push_back(ntmp);
	strLog.Format("订阅主题1: %d", ntmp);
	m_pLogFile->Add((char *)strLog.c_str(),CLogFile::trace);
    
    memset(ctmp, 0, sizeof(ctmp));
	GetIniKey(ZX_BUS_DEF_INI, "BUS_EVENT", "command", ctmp);
	ntmp = atoi(ctmp);     
	m_CmdCode = ntmp;
    node.vEventTopicList.push_back(ntmp);
	strLog.Format("command通道: %d", ntmp);
	m_pLogFile->Add((char *)strLog.c_str(),CLogFile::trace);

	memset(ctmp, 0, sizeof(ctmp));
	GetIniKey(ZX_BUS_DEF_INI, "BUS_EVENT", "event", ctmp);
	ntmp = atoi(ctmp);
	m_EventCode = ntmp;
	node.vEventTopicList.push_back(ntmp);
	strLog.Format("订阅主题2: %d", ntmp);
	m_pLogFile->Add((char *)strLog.c_str(),CLogFile::trace);
// 全收
//	node.bIsCareSttpInfo = false;      //sttp消息ID的关注类型:true-订阅sttp ID列表中的信息 false-不订阅列表中的信息
//	strLog.Format("sttp消息ID的关注类型: %d", node.bIsCareSttpInfo );
//	m_pLogFile->Add((char *)strLog.c_str(),CLogFile::trace);
//指定收
    node.bIsCareSttpInfo = true; 
    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::_fill_APP_NODE_INFO()]sttp消息ID的关注类型: bIsCareSttpInfo[%d]", node.bIsCareSttpInfo);
    node.vSttpMsgList.push_back(203);
    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::_fill_APP_NODE_INFO()]sttp消息ID的关注类型: %d",203);
    node.vSttpMsgList.push_back(210);
    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::_fill_APP_NODE_INFO()]sttp消息ID的关注类型: %d",210);
    node.vSttpMsgList.push_back(213);
    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::_fill_APP_NODE_INFO()]sttp消息ID的关注类型: %d",213);
    node.vSttpMsgList.push_back(20007);
    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::_fill_APP_NODE_INFO()]sttp消息ID的关注类型: %d",20007);
    node.vSttpMsgList.push_back(20011);
    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::_fill_APP_NODE_INFO()]sttp消息ID的关注类型: %d",20011);
    node.vSttpMsgList.push_back(20015);
    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::_fill_APP_NODE_INFO()]sttp消息ID的关注类型: %d",20015);
    node.vSttpMsgList.push_back(20173);
    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::_fill_APP_NODE_INFO()]sttp消息ID的关注类型: %d",20173);
    node.vSttpMsgList.push_back(40003);
    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::_fill_APP_NODE_INFO()]sttp消息ID的关注类型: %d",40003);
    node.vSttpMsgList.push_back(20165);
    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::_fill_APP_NODE_INFO()]sttp消息ID的关注类型: %d",20165);
    node.vSttpMsgList.push_back(40005);
    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::_fill_APP_NODE_INFO()]sttp消息ID的关注类型: %d",40005);
    node.vSttpMsgList.push_back(40004);
    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::_fill_APP_NODE_INFO()]sttp消息ID的关注类型: %d",40004);
    
//	bool              bIsCareSttpInfo;      //sttp消息ID的关注类型:true-订阅sttp ID列表中的信息 false-不订阅列表中的信息
//	vector<int>       vSttpMsgList;         //sttp消息ID列表
	memset(ctmp, 0, sizeof(ctmp));
	GetIniKey(ZX_BUS_DEF_INI, "BUS_BASIC_INF", "app_name", ctmp);
	node.strAppName = ctmp;
	strLog.Format("应用名: %s", node.strAppName.c_str());
	m_pLogFile->Add((char *)strLog.c_str(),CLogFile::trace);

	memset(ctmp, 0, sizeof(ctmp));
	GetIniKey(ZX_BUS_DEF_INI, "BUS_BASIC_INF", "context_name", ctmp);
	node.strContextName = ctmp;
	strLog.Format("态名: %s", node.strContextName.c_str());
	m_pLogFile->Add((char *)strLog.c_str(),CLogFile::trace);

	return nRet;
}
int CTaskMngr::_bus_swap_callback(void * pRegObj, STTP_FULL_DATA& sttp_data, string& strAppNodexName, BUS_ADDITION_INFO & addition_info)
{
	int nRet = 0;
	char cErr[255]="";
	CTaskMngr *pObj = (CTaskMngr *)pRegObj;

    if ( (203 == sttp_data.sttp_head.uMsgID)\
      ||(210 == sttp_data.sttp_head.uMsgID)\
      ||(213 == sttp_data.sttp_head.uMsgID)\
      ||(20007 == sttp_data.sttp_head.uMsgID)\
      ||(20011 == sttp_data.sttp_head.uMsgID)\
      ||(20015 == sttp_data.sttp_head.uMsgID)\
      ||(20173 == sttp_data.sttp_head.uMsgID)\
      ||(40003 == sttp_data.sttp_head.uMsgID)\
      ||(20165 == sttp_data.sttp_head.uMsgID)\
      ||(40005 == sttp_data.sttp_head.uMsgID) \
      ||(40004 == sttp_data.sttp_head.uMsgID) )
    {
        CLockUp tmpLock(&pObj->m_LockForRcvBusInfList);
        sprintf(cErr,"【_bus_swap_callback】收到需要处理的报文（msgID=%d）rii[%d],放入队列,插入前队列数量：%d.", sttp_data.sttp_head.uMsgID, sttp_data.sttp_head.uMsgRii, pObj->m_RcvBusInfList.size());
        pObj->m_pLogFile->Add(cErr,CLogFile::trace);
        BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
        Sttp_Info.sttp_data = sttp_data;
        Sttp_Info.t_time = time(NULL);
        pObj->m_RcvBusInfList.push_back(Sttp_Info);
    }/*else{
        pObj->m_pLogFile->FormatAdd(CLogFile::trace,"【_bus_swap_callback】收到不需要处理的报文（msgID=%d）rii[%d] body：nStatus[%d] msgid[%d] hander[%s] msg[%s] event[%d] chginfotype[%d] cpu[%d] flag[%d]",
                sttp_data.sttp_head.uMsgID,
                sttp_data.sttp_head.uMsgRii,
                sttp_data.sttp_body.nStatus,
                sttp_data.sttp_body.nMsgId,
                sttp_data.sttp_body.ch_HandlerName,
                sttp_data.sttp_body.strMessage.c_str(),
                sttp_data.sttp_body.nEventType,
                sttp_data.sttp_body.nChangeInfoType,
                sttp_data.sttp_body.nCpu,
                sttp_data.sttp_body.nFlag);
    }*/


	return nRet;
}
int CTaskMngr::_bus_conn_callback(void* pParam, int pStatus, void* pReserved)
{
    CTaskMngr* pObj = (CTaskMngr*)pParam;
    pObj->_BusLinkStatusHandle(pStatus, pReserved);
}
int CTaskMngr::_BusLinkStatusHandle(int nStatus, void* pReserved)
{
    char cError[255] = "";
    m_nLinkStatus = nStatus;
    sprintf(cError, "与总线的链路状态变为:%d", m_nLinkStatus);
    m_pLogFile->FormatAdd(CLogFile::trace, cError);
    return 0;
}
int CTaskMngr::_sendSttp2Msgbus(const STTP_FULL_DATA& SttpData)
{
	int nRet = 0;

	if (NULL == m_pIZxBusSwap)
	{
		return 1;
	}
	//	short nMsgTopicType = ZX_MSG_TOPIC_COMMAND;
	string strAppNodeName = EXE_NAME;
	int nSttpType = 3;//1-请求；2-应答；3-主动上送
	map<int,int>::iterator iteSttpType = m_mSttpType.find( SttpData.sttp_head.uMsgID );
	if ( iteSttpType != m_mSttpType.end() )
	{
		nSttpType = iteSttpType->second;
	}
	switch ( nSttpType )
	{
	case 1:
		nRet = m_pIZxBusSwap->SendSttpMsgData(SttpData, m_CmdCode, strAppNodeName, m_uSendchnl_down);
		break;
	case 2:
		nRet = m_pIZxBusSwap->SendSttpMsgData(SttpData, m_RespCode, strAppNodeName, m_uSendchnl_up);
		break;
	default:
		nRet = m_pIZxBusSwap->SendSttpMsgData(SttpData, m_EventCode, strAppNodeName, m_uSendchnl_up);
	}

	return nRet;
}

char * CTaskMngr::__CvtGbk2Utf8(const char * cObj)
{
	memset(m_cChr,0,MAX_CHAR_BUFF_LEN);
    if(cObj!=NULL) {
        m_CharCvtObj.convert_gbk_utf8(cObj,m_cChr,MAX_CHAR_BUFF_LEN-1);
    }
	return m_cChr;
}
char * CTaskMngr::__CvtUtf82Gbk(const char * cObj)
{
	memset(m_cChr,0,MAX_CHAR_BUFF_LEN);
    if(cObj!=NULL) {
        m_CharCvtObj.convert_utf8_gbk(cObj,m_cChr,MAX_CHAR_BUFF_LEN-1);
    }
	return m_cChr;
}
char * CTaskMngr::mystrtok(char * s,const char * ct) 
{
    char *sbegin, *send;
    sbegin  = s ? s : myStrtok;
    if (!sbegin) { return NULL;  }
    int a=strspn(sbegin,ct);//返回字符串中第一个不在指定字符串中出现的字符下标
    if(a == 1){
        sbegin += 0;//不跳过,,返回""
    }else{
        sbegin += a;
    }
    if (*sbegin == '\0'){
		myStrtok = NULL;
		return NULL;
    }
    send = strpbrk(sbegin, ct);
    if(send && *send != '\0')
		*send++ = '\0';
	myStrtok = send;
    return (sbegin);
}
bool CTaskMngr::InitCvtLib()
{
	if ( !m_CharCvtObj.load_lib()) {
		m_pLogFile->FormatAdd(CLogFile::error,"[CStationInfo103::Init()] 加载 字符集编码转换接口库失败.");
		return false;
	}
    m_pLogFile->FormatAdd(CLogFile::trace,"[CStationInfo103::Init()] 加载 字符集编码转换接口库 ok.");
	return true;
}
bool CTaskMngr::UnInitCvtLib()
{
	if ( !m_CharCvtObj.unload_lib()) {
		m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::UnInitCvtLib()] 卸载 字符集编码转换接口库失败.");
		return false;
	}
    if(m_pLogFile!=NULL){
        m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::UnInitCvtLib()] 卸载 字符集编码转换接口库 ok.");
    }
	return true;
}
int CTaskMngr::PrintBytes(const string &str)
{
    if(!m_LogCfg.bShowDebug) return 0;
    vector<BYTE> vBytes;
    vBytes.insert(vBytes.end(),str.begin(),str.end());
    
   m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::PrintBytes2]");
   string str1;str1.clear();
    for(vector<BYTE>::iterator it=vBytes.begin();it!=vBytes.end();it++){
        char c20[20];
        snprintf(c20,20,"%02x ",(unsigned char)*it);
        str1=str1+c20;
    }
   m_pLogFile->FormatAdd(CLogFile::trace,"%s",str1.c_str());
   m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::PrintBytes]  end ");
}
int CTaskMngr::PrintBytes(vector<BYTE> &vBytes)
{
    if(!m_LogCfg.bShowDebug) return 0;
    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::PrintBytes]");
    for(vector<BYTE>::iterator it=vBytes.begin();it!=vBytes.end();it++){
       printf("%02X ",*it);
    }
    m_pLogFile->FormatAdd(CLogFile::trace,"\n[CTaskMngr::PrintBytes]  end ");
}
int CTaskMngr::PrintBytes(char *message,const int message_len)
{
    if(!m_LogCfg.bShowDebug) return 0;
    m_pLogFile->FormatAdd(CLogFile::trace,"[CTaskMngr::PrintBytes]");
    for(int i=0;i<message_len;i++){
       printf("%02X ",message[i]);
    }
    m_pLogFile->FormatAdd(CLogFile::trace,"\n[CTaskMngr::PrintBytes]  end ");
}
//0-正常 <0-失败 1-文件相关走固定ied填充   a0 2-a1 3-bf48 4-9f49 5-9f4a
int CTaskMngr::DecodeMMsType(const string &str)
{
    if(str.length()<27){
        printf("mms file 报文长度[%d]<27 不判类型\n",str.length());
    }
    BYTE cType = str.at(20);//0xa0-请求 0xa1-回复
    //char cLen = str.at(21);//长度
    BYTE cIdLen = str.at(22);//0x02-2位接下来
    if( (cType==0xa0)||(cType==0xa1) ){
        printf("mms file  cType[%02x][0xa0-请求 0xa1-回复]\n",cType);
        BYTE cSrv1;
        BYTE cSrv2;
        if(cIdLen==0x02){
            cSrv1 = str.at(25);
            cSrv2 = str.at(26);
        }else
        if(cIdLen==0x01){
            cSrv1=str.at(24);
            cSrv2=str.at(25);
        }else{
            printf("mms file  cIdLen[%02x]非2和1\n",cType);
            return 0;
        }
        
        if(0xa0==cSrv1) {return 1;}
        if(0xa1==cSrv1) {return 1;}
        if( (0xbf==cSrv1)&&(0x48==cSrv2) ) {return 1;}
        if( (0x9f==cSrv1)&&(0x49==cSrv2) ) {return 1;}
        if( (0x9f==cSrv1)&&(0x4a==cSrv2) ) {return 1;}
    }
    
    
    
    return 0;
}
//0-不是last要另存等全了判定转发，1-是Last可以透传, -1-判断失败
int CTaskMngr::HasLastCOTP(string strStn,int nChNo,string &strMsg)
{
    if(strMsg.length()<7){
        m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::HasLastCOTP]通道[%d]mms tpkt报文长度[%d]<7 不判是否last cotp",strStn.c_str(),nChNo,strMsg.length());
        return -1;
    }
    BYTE cTPDU = strMsg.at(6);//0x80 or 0x00
    BYTE cLastDataUnit = cTPDU & 0x80;//1000 0000;1-yes 0-no
    
    if(cLastDataUnit == 0x80){//1-yes
        m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::HasLastCOTP]通道[%d]mms cTPDU[%02X]LastFlag[%02X] tpkt报文长度[%d] 是 last cotp，可以透传",strStn.c_str(),nChNo,
                cTPDU,cLastDataUnit,strMsg.length());
        return 1;
    }else if(cLastDataUnit == 0x00){//0-no
        m_pLogFile->FormatAdd(CLogFile::trace,"[%s][CTaskMngr::HasLastCOTP]通道[%d]mms cTPDU[%02X]LastFlag[%02X] tpkt报文长度[%d] 不是 last cotp，不能透传，另存待完整后判断透传",strStn.c_str(),nChNo,
                cTPDU,cLastDataUnit,strMsg.length());
        return 0;
    }else{
        m_pLogFile->FormatAdd(CLogFile::error,"[%s][CTaskMngr::HasLastCOTP]通道[%d]mms cTPDU[%02X]LastFlag[%02X] tpkt报文长度[%d] 是否lastcotp 判定异常",strStn.c_str(),nChNo,
                cTPDU,cLastDataUnit,strMsg.length());
        return -1;
    }
    return -1;
}

 void CTaskMngr::logChange(bool bIsDebug)
 {
     for(map<string,STN_CFG>::iterator itStn=mapStnLinkCfg.begin();itStn!=mapStnLinkCfg.end();itStn++)
     {
         map<string,push103* >::iterator it103=map103Push.find(itStn->first);
         if(it103!=map103Push.end())
         {
             it103->second->setIsDebug(bIsDebug);
         }
     }
 }

void CTaskMngr::HandleFrontLinkChange()
{
    int nTemSta = -1;
    int n102Sta = -1;
    for(map<string,push* >::iterator it102=map102Push.begin();it102!=map102Push.end();it102++)
    {
       n102Sta= it102->second->getLinkSta();
       if(m_mapFntSta.find(it102->first)!= m_mapFntSta.end())
       {
           nTemSta = m_mapFntSta[it102->first];
           if(n102Sta != nTemSta)
           {
               map<string,STN_CFG>::iterator itGet = mapStnLinkCfg.find(it102->first);
               if(itGet != mapStnLinkCfg.end())
               {
                   //与前置的通断不影响通道设备连接状态，不发送20002
                   //m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::HandleFrontLinkChange()] stn[%s], 61850前置连接成功发送所有设备20002 ",it102->first.c_str());
                   //itGet->second.sApciPoiner.pStnFlow->SendAllPtChNo20002();
               }
               m_pLogFile->FormatAdd(CLogFile::error,"[CTaskMngr::HandleFrontLinkChange()] stn[%s], 61850前置状态改变 %d-->%d [1:连接，0:关闭]",it102->first.c_str(),nTemSta,n102Sta);
               m_mapFntSta[it102->first] = n102Sta;

               // 同步厂站状态到服务器在线管理器
               UpdateStationCommStatus(it102->first, n102Sta);
           }   
       }
    }
}

// 从数据库中读取子站通信状态自动上送周期.
int CTaskMngr::__ReadSstnStatusAutoCyl()
{
	if (m_pDBAcess == NULL) //DBFacade指针无效 直接返回失败
	{
		m_pLogFile->FormatAdd(CLogFile::error,"读取厂站通信状态自动上送周期失败,原因为数据库接口函数指针无效." );
		return 0;
	}

	CMemSet pMemSet;
	SQL_DATA pSqlData;
	pSqlData.Conditionlist.clear();
	pSqlData.Fieldlist.clear();

	char cError[255] = "";
	//取厂站ID
	AddField(pSqlData,"t_report",EX_STTP_DATA_TYPE_INT);


	try
	{
		m_pDBAcess->Select(EX_STTP_INFO_FUN_TIMEOUT_CFG,pSqlData,cError,&pMemSet);
	}
	catch (...)
	{
		m_pLogFile->FormatAdd(CLogFile::error,"读取表tb_commu_timeout_config异常");
		return 0;
	}

	int nSetNum = pMemSet.GetMemRowNum();
	if ( nSetNum > 0 )
	{
		pMemSet.MoveFirst();
		return atoi(pMemSet.GetValue(UINT(0)));
	}
	return 0;
}

void CTaskMngr::_SttpTypeCatch()
{
	m_mSttpType[203] = 1;
	m_mSttpType[205] = 1;
	m_mSttpType[210] = 1;
	m_mSttpType[213] = 1;
	m_mSttpType[216] = 1;
	m_mSttpType[20003] = 1;
	m_mSttpType[20007] = 1;
	m_mSttpType[20011] = 1;
	m_mSttpType[20015] = 1;
	m_mSttpType[20019] = 1;
	m_mSttpType[20023] = 1;
	m_mSttpType[20025] = 1;
	m_mSttpType[20042] = 1;
	m_mSttpType[20047] = 1;
	m_mSttpType[20051] = 1;
	m_mSttpType[20053] = 1;
	m_mSttpType[20055] = 1;
	m_mSttpType[20057] = 1;
	m_mSttpType[20059] = 1;
	m_mSttpType[20063] = 1;
	m_mSttpType[20081] = 1;
	m_mSttpType[20085] = 1;
	m_mSttpType[20088] = 1;
	m_mSttpType[20105] = 1;
	m_mSttpType[20106] = 1;
	m_mSttpType[20108] = 1;
	m_mSttpType[20110] = 1;
	m_mSttpType[20113] = 1;
	m_mSttpType[20117] = 1;
	m_mSttpType[20119] = 1;
	m_mSttpType[20121] = 1;
	m_mSttpType[20123] = 1;
	m_mSttpType[20127] = 1;
	m_mSttpType[20128] = 1;
	m_mSttpType[20129] = 1;
	m_mSttpType[20137] = 1;
	m_mSttpType[20145] = 1;
	m_mSttpType[20147] = 1;
	m_mSttpType[20148] = 1;
	m_mSttpType[20152] = 1;
	m_mSttpType[20153] = 1;
	m_mSttpType[20154] = 1;
	m_mSttpType[20155] = 1;
	m_mSttpType[20158] = 1;
	m_mSttpType[20159] = 1;
	m_mSttpType[20162] = 1;
	m_mSttpType[20163] = 1;
	m_mSttpType[20165] = 1;
	m_mSttpType[20169] = 1;
	m_mSttpType[20171] = 1;
	m_mSttpType[20173] = 1;
	m_mSttpType[20175] = 1;
	m_mSttpType[20176] = 1;
	m_mSttpType[20178] = 1;
	m_mSttpType[20500] = 1;
	m_mSttpType[20502] = 1;
	m_mSttpType[20504] = 1;
	m_mSttpType[20506] = 1;
	m_mSttpType[20510] = 1;
	m_mSttpType[20512] = 1;
	m_mSttpType[20580] = 1;
	m_mSttpType[20582] = 1;
	m_mSttpType[20584] = 1;
	m_mSttpType[20585] = 1;
	m_mSttpType[20600] = 1;
	m_mSttpType[20700] = 1;
	m_mSttpType[30015] = 1;
	m_mSttpType[30025] = 1;
	m_mSttpType[30042] = 1;
	m_mSttpType[30051] = 1;
	m_mSttpType[30123] = 1;
	m_mSttpType[30173] = 1;
	m_mSttpType[30176] = 1;
	m_mSttpType[30179] = 1;
	m_mSttpType[30181] = 1;
	m_mSttpType[40001] = 1;
	m_mSttpType[40003] = 1;
	m_mSttpType[40005] = 1;
	m_mSttpType[40007] = 1;
	m_mSttpType[61850] = 1;
	m_mSttpType[61851] = 1;
	m_mSttpType[61880] = 1;
	
	m_mSttpType[204] = 2;
	m_mSttpType[206] = 2;
	m_mSttpType[211] = 2;
	m_mSttpType[212] = 2;
	m_mSttpType[215] = 2;
	m_mSttpType[901] = 2;
	m_mSttpType[20004] = 2;
	m_mSttpType[20008] = 2;
	m_mSttpType[20012] = 2;
	m_mSttpType[20016] = 2;
	m_mSttpType[20020] = 2;
	m_mSttpType[20024] = 2;
	m_mSttpType[20026] = 2;
	m_mSttpType[20043] = 2;
	m_mSttpType[20044] = 2;
	m_mSttpType[20048] = 2;
	m_mSttpType[20052] = 2;
	m_mSttpType[20054] = 2;
	m_mSttpType[20056] = 2;
	m_mSttpType[20058] = 2;
	m_mSttpType[20060] = 2;
	m_mSttpType[20069] = 2;
	m_mSttpType[20082] = 2;
	m_mSttpType[20086] = 2;
	m_mSttpType[20089] = 2;
	m_mSttpType[20107] = 2;
	m_mSttpType[20109] = 2;
	m_mSttpType[20111] = 2;
	m_mSttpType[20114] = 2;
	m_mSttpType[20116] = 2;
	m_mSttpType[20118] = 2;
	m_mSttpType[20120] = 2;
	m_mSttpType[20124] = 2;
	m_mSttpType[20125] = 2;
	m_mSttpType[20146] = 2;
	m_mSttpType[20151] = 2;
	m_mSttpType[20157] = 2;
	m_mSttpType[20166] = 2;
	m_mSttpType[20167] = 2;
	m_mSttpType[20170] = 2;
	m_mSttpType[20172] = 2;
	m_mSttpType[20174] = 2;
	m_mSttpType[20177] = 2;
	m_mSttpType[20301] = 2;
	m_mSttpType[20302] = 2;
	m_mSttpType[20501] = 2;
	m_mSttpType[20503] = 2;
	m_mSttpType[20505] = 2;
	m_mSttpType[20507] = 2;
	m_mSttpType[20511] = 2;
	m_mSttpType[20533] = 2;
	m_mSttpType[20535] = 2;
	m_mSttpType[20581] = 2;
	m_mSttpType[20583] = 2;
	m_mSttpType[20586] = 2;
	m_mSttpType[20601] = 2;
	m_mSttpType[20701] = 2;
	m_mSttpType[30016] = 2;
	m_mSttpType[30026] = 2;
	m_mSttpType[30043] = 2;
	m_mSttpType[30052] = 2;
	m_mSttpType[30124] = 2;
	m_mSttpType[30171] = 2;
	m_mSttpType[30174] = 2;
	m_mSttpType[30177] = 2;
	m_mSttpType[30183] = 2;
	m_mSttpType[40002] = 2;
	m_mSttpType[40004] = 2;
	m_mSttpType[40006] = 2;
	m_mSttpType[40008] = 2;
	m_mSttpType[61881] = 2;
	m_mSttpType[61901] = 2;
}

/*************************************************************
 函 数 名:   GetSrvOnlineMngrConfigFromDB
 功能概要:   从数据库读取服务器在线管理器配置
 返 回 值:   bool true-成功 false-失败
**************************************************************/
bool CTaskMngr::GetSrvOnlineMngrConfigFromDB()
{
    bool bResult = true; // 默认成功，因为这是可选配置
    if (m_pDBAcess == NULL) {
        m_pLogFile->Add("[GetSrvOnlineMngrConfigFromDB] 数据库指针为空，跳过数据库配置读取", CLogFile::warning);
        return bResult;
    }

    try {
        CMemSet pMemSet;
        char sError[255] = "";

        SQL_DATA pSqlData;
        pSqlData.Conditionlist.clear();
        pSqlData.Fieldlist.clear();

        // 添加查询字段 - 使用项目标准的AddField方法
        AddField(pSqlData, "msg_log", EX_STTP_DATA_TYPE_INT);

        // 使用项目标准的数据库访问方式
        REALDATA_CONDITION pCondition;
        pCondition.IsUse = true;

        if (m_pDBAcess->RDSelect(EX_STTP_INFO_LOCAL_CFG, pSqlData, pCondition, sError, &pMemSet)) {
            if (pMemSet.GetMemRowNum() > 0) {
                // 读取报文日志配置
                pMemSet.MoveFirst();
                int msgLog = atoi(pMemSet.GetValue(0).c_str()); // msg_log字段
                m_pLogFile->FormatAdd(CLogFile::trace, "[GetSrvOnlineMngrConfigFromDB] 从数据库读取msg_log配置: %d", msgLog);
                bResult = true;
            } else {
                m_pLogFile->Add("[GetSrvOnlineMngrConfigFromDB] 未找到服务器在线管理器配置记录", CLogFile::warning);
            }
        } else {
            m_pLogFile->FormatAdd(CLogFile::warning, "[GetSrvOnlineMngrConfigFromDB] 查询配置失败: %s", sError);
        }

    } catch (...) {
        m_pLogFile->Add("[GetSrvOnlineMngrConfigFromDB] 读取数据库配置异常", CLogFile::error);
    }

    return bResult;
}

/*************************************************************
 函 数 名:   InitSrvOnlineManager
 功能概要:   初始化服务器在线管理器
 返 回 值:   int 0-成功 其他-失败
**************************************************************/
int CTaskMngr::InitSrvOnlineManager()
{
    try {
        // 1. 创建服务器在线管理器对象
        m_pSrvOnlineMngr = new CXJSrvOnlineManager();
        if (m_pSrvOnlineMngr == NULL) {
            m_pLogFile->Add("[InitSrvOnlineManager] 创建服务器在线管理器对象失败", CLogFile::error);
            return -1;
        }

        // 2. 初始化动态库
        char errorMsg[256] = "";
        if (!m_pSrvOnlineMngr->InitLibrary(errorMsg)) {
            m_pLogFile->FormatAdd(CLogFile::error, "[InitSrvOnlineManager] 初始化服务器在线管理器库失败: %s", errorMsg);
            delete m_pSrvOnlineMngr;
            m_pSrvOnlineMngr = NULL;
            return -1;
        }

        // 3. 准备配置参数
        stXJSrvOnlineManager srvMngr;
        memset(&srvMngr, 0, sizeof(srvMngr));

        // 从配置文件读取服务器ID
        char serverID[64] = "";
        GetIniKey(ZCS_SERVER_INI, "SERVER", "serverid", serverID);
        if (strlen(serverID) == 0) {
            m_pLogFile->Add("[InitSrvOnlineManager] 从配置文件读取服务器ID失败", CLogFile::error);
            delete m_pSrvOnlineMngr;
            m_pSrvOnlineMngr = NULL;
            return -1;
        }
        strncpy(srvMngr.chServerID, serverID, sizeof(srvMngr.chServerID) - 1);

        // 设置日志配置（与104Vlan保持一致）
        strncpy(srvMngr.szLog_path, m_LogCfg.strLogPath.c_str(), sizeof(srvMngr.szLog_path) - 1);
        srvMngr.iLog_level = m_LogCfg.nLogLevel;
        srvMngr.iLogDay = m_LogCfg.nLogRrdDay;

        // 从数据库读取报文日志配置（可选）
        srvMngr.bRecordMsg = false;  // 默认值
        GetSrvOnlineMngrConfigFromDB();  // 尝试从数据库读取配置

        // 设置前置类型为 104Vlan
        srvMngr.iReserved = 2;  // 2-104Vlan

        // 4. 注册回调函数
        memset(errorMsg, 0, sizeof(errorMsg));
        if (m_pSrvOnlineMngr->RegisterSrvSwitchCallback(_srv_switch_callback, this, errorMsg) != 0) {
            m_pLogFile->FormatAdd(CLogFile::error, "[InitSrvOnlineManager] 注册服务器切换回调失败: %s", errorMsg);
            delete m_pSrvOnlineMngr;
            m_pSrvOnlineMngr = NULL;
            return -1;
        }

        // 5. 启动服务器在线管理器
        memset(errorMsg, 0, sizeof(errorMsg));
        if (m_pSrvOnlineMngr->StartSrvOnLineManager(srvMngr, errorMsg) != 0) {
            m_pLogFile->FormatAdd(CLogFile::error, "[InitSrvOnlineManager] 启动服务器在线管理器失败: %s", errorMsg);
            delete m_pSrvOnlineMngr;
            m_pSrvOnlineMngr = NULL;
            return -1;
        }

        m_bSrvOnlineMngrEnabled = true;
        m_pLogFile->FormatAdd(CLogFile::info, "[InitSrvOnlineManager] 服务器在线管理器启动成功，服务器ID: %s", serverID);
        return 0;

    } catch (...) {
        m_pLogFile->Add("[InitSrvOnlineManager] 初始化服务器在线管理器异常", CLogFile::error);
        if (m_pSrvOnlineMngr != NULL) {
            delete m_pSrvOnlineMngr;
            m_pSrvOnlineMngr = NULL;
        }
        return -1;
    }
}

/*************************************************************
 函 数 名:   _srv_switch_callback
 功能概要:   服务器切换回调函数（静态）
 返 回 值:   int 0-成功 其他-失败
**************************************************************/
int CTaskMngr::_srv_switch_callback(void* pParam, int pStatus, std::vector<stXJSubstation>& pStationList)
{
    CTaskMngr* pThis = (CTaskMngr*)pParam;
    if (pThis != NULL) {
        return pThis->OnServerSwitch(pStatus, pStationList);
    }
    return -1;
}

/*************************************************************
 函 数 名:   OnServerSwitch
 功能概要:   处理服务器切换事件
 返 回 值:   int 0-成功 其他-失败
**************************************************************/
int CTaskMngr::OnServerSwitch(int pStatus, std::vector<stXJSubstation>& pStationList)
{
    try {
        m_pLogFile->FormatAdd(CLogFile::info, "[OnServerSwitch] 收到服务器切换通知，状态: %d, 厂站数量: %d",
                             pStatus, pStationList.size());

        if (pStatus == SRV_ONLINE_ACTIVE) {
            // 服务器在线状态：遍历内存中的厂站列表A，判断是否在回调列表B中
            m_pLogFile->FormatAdd(CLogFile::info, "[OnServerSwitch] 服务器在线，根据厂站列表进行启停操作");

            for (map<string,STN_CFG>::iterator itStn = mapStnLinkCfg.begin(); itStn != mapStnLinkCfg.end(); itStn++) {
                std::string stationId = itStn->first;
                bool bFoundInCallback = false;

                // 在回调列表中查找当前厂站ID
                for (size_t i = 0; i < pStationList.size(); i++) {
                    if (pStationList[i].strStationId == stationId) {
                        bFoundInCallback = true;
                        break;
                    }
                }

                if (bFoundInCallback) {
                    // 厂站在回调列表中，启动厂站线程
                    m_pLogFile->FormatAdd(CLogFile::info, "[OnServerSwitch] 启动厂站: %s", stationId.c_str());
                    StartStationProcess(stationId);
                } else {
                    // 厂站不在回调列表中，停止厂站线程
                    m_pLogFile->FormatAdd(CLogFile::info, "[OnServerSwitch] 停止厂站: %s", stationId.c_str());
                    StopStationProcess(stationId);
                }
            }
        } else {
            // 服务器非在线状态：遍历内存中的厂站列表A，停止所有厂站线程
            m_pLogFile->FormatAdd(CLogFile::info, "[OnServerSwitch] 服务器非在线状态(%d)，停止所有厂站线程", pStatus);

            for (map<string,STN_CFG>::iterator itStn = mapStnLinkCfg.begin(); itStn != mapStnLinkCfg.end(); itStn++) {
                std::string stationId = itStn->first;
                m_pLogFile->FormatAdd(CLogFile::info, "[OnServerSwitch] 停止厂站: %s", stationId.c_str());
                StopStationProcess(stationId);
            }
        }

        return 0;

    } catch (...) {
        m_pLogFile->Add("[OnServerSwitch] 处理服务器切换事件异常", CLogFile::error);
        return -1;
    }
}

/*************************************************************
 函 数 名:   StartStationProcess
 功能概要:   启动厂站进程（服务器切换时动态启动）
 返 回 值:   int 0-成功 其他-失败
**************************************************************/
int CTaskMngr::StartStationProcess(const std::string& stationId)
{
    try {
        // 检查厂站配置是否存在
        std::map<std::string, STN_CFG>::iterator itStnCfg = mapStnLinkCfg.find(stationId);
        if (itStnCfg == mapStnLinkCfg.end()) {
            m_pLogFile->FormatAdd(CLogFile::error, "[StartStationProcess] 厂站 %s 配置不存在", stationId.c_str());
            return -1;
        }

        // 检查是否已经启动
        if (map102Push.find(stationId) != map102Push.end() && map102Push[stationId] != NULL) {
            m_pLogFile->FormatAdd(CLogFile::warning, "[StartStationProcess] 厂站 %s 进程已经启动", stationId.c_str());
            return 0;
        }

        // 查找对应的监听IP配置
        std::string bindIp = "";
        for (std::map<std::string, std::string>::iterator it = sListen61850.mapBindIpStnId.begin();
             it != sListen61850.mapBindIpStnId.end(); it++) {
            if (it->second == stationId) {
                bindIp = it->first;
                break;
            }
        }

        if (bindIp.empty()) {
            m_pLogFile->FormatAdd(CLogFile::error, "[StartStationProcess] 厂站 %s 未找到对应的监听IP配置", stationId.c_str());
            return -1;
        }

        // 创建专用的消息日志对象（参考Pre102()的实现）
        CMessageLog* MessageLog = new CMessageLog;
        if (MessageLog == NULL) {
            m_pLogFile->FormatAdd(CLogFile::error, "[StartStationProcess] 厂站 %s 创建MessageLog失败", stationId.c_str());
            return -1;
        }

        MessageLog->Close();
        MessageLog->SetLogLevel(m_LogCfg.nLogLevel);
        std::string logPath = m_LogCfg.strLogPath + "/" + stationId + "/" + bindIp + "/";
        MessageLog->SetLogPath(logPath.c_str());
        MessageLog->SetLogSaveDays(m_LogCfg.nLogRrdDay);
        MessageLog->Open("102MessageLogs");
        MessageLog->FormatAdd(CLogFile::trace, "102MessageLogs 报文保留天数为:%d.", m_LogCfg.nLogRrdDay);

        // 创建 push 对象
        push* p_102Push = new push(MessageLog, m_pExit);
        if (p_102Push == NULL) {
            m_pLogFile->FormatAdd(CLogFile::error, "[StartStationProcess] 厂站 %s 创建push对象失败", stationId.c_str());
            delete MessageLog;
            return -1;
        }

        // 启动 push 对象（参考Pre102()的实现）
        std::map<std::string, pthread_t> mapThreadIds;
        mapThreadIds.clear();
        int ret = p_102Push->start(bindIp, sListen61850.nPort, stationId, mapThreadIds);

        if (ret == 0) {
            // 保存到映射表
            map102Push.insert(std::make_pair(stationId, p_102Push));
            map102PushTheadId.insert(std::make_pair(stationId, mapThreadIds));

            // 记录线程信息
            for (std::map<std::string, pthread_t>::iterator itTh = mapThreadIds.begin();
                 itTh != mapThreadIds.end(); itTh++) {
                m_pLogFile->FormatAdd(CLogFile::trace,
                    "[StartStationProcess] 厂站 %s 启动成功 - IP[%s] Port[%d] Thread[%s] ThreadId[%ld]",
                    stationId.c_str(), bindIp.c_str(), sListen61850.nPort,
                    itTh->first.c_str(), itTh->second);
            }

            m_pLogFile->FormatAdd(CLogFile::info, "[StartStationProcess] 厂站 %s 进程启动成功", stationId.c_str());
            return 0;
        } else {
            m_pLogFile->FormatAdd(CLogFile::error,
                "[StartStationProcess] 厂站 %s 启动失败 - IP[%s] Port[%d] ret[%d]",
                stationId.c_str(), bindIp.c_str(), sListen61850.nPort, ret);
            delete p_102Push;
            delete MessageLog;
            return -1;
        }

    } catch (...) {
        m_pLogFile->FormatAdd(CLogFile::error, "[StartStationProcess] 启动厂站 %s 进程异常", stationId.c_str());
        return -1;
    }
}

/*************************************************************
 函 数 名:   StopStationProcess
 功能概要:   停止厂站进程
 返 回 值:   int 0-成功 其他-失败
**************************************************************/
int CTaskMngr::StopStationProcess(const std::string& stationId)
{
    try {
        // 查找并停止 push 对象
        std::map<std::string, push*>::iterator it = map102Push.find(stationId);
        if (it != map102Push.end() && it->second != NULL) {
            push* pPush = it->second;

            // 等待并清理相关线程（参考End()方法的实现）
            std::map<std::string, std::map<std::string, pthread_t>>::iterator itThread = map102PushTheadId.find(stationId);
            if (itThread != map102PushTheadId.end()) {
                for (std::map<std::string, pthread_t>::iterator itTh = itThread->second.begin();
                     itTh != itThread->second.end(); itTh++) {
                    pthread_t ThreadId = itTh->second;
                    if (ThreadId != 0) {
                        int nRet = pthread_join(ThreadId, NULL);
                        if (nRet != 0) {
                            m_pLogFile->FormatAdd(CLogFile::trace,
                                "[StopStationProcess] 厂站 %s 退出线程 %s ThreadId[%ld] 异常 ret[%d]: %s",
                                stationId.c_str(), itTh->first.c_str(), ThreadId, nRet, strerror(errno));
                        } else {
                            m_pLogFile->FormatAdd(CLogFile::trace,
                                "[StopStationProcess] 厂站 %s 退出线程 %s ThreadId[%ld] 成功",
                                stationId.c_str(), itTh->first.c_str(), ThreadId);
                        }
                    }
                }
                // 从线程映射表中移除
                map102PushTheadId.erase(itThread);
            }

            // 删除 push 对象（push的析构函数应该会处理资源清理）
            delete pPush;

            // 从映射表中移除
            map102Push.erase(it);

            m_pLogFile->FormatAdd(CLogFile::info, "[StopStationProcess] 厂站 %s 进程停止成功", stationId.c_str());
        } else {
            m_pLogFile->FormatAdd(CLogFile::warning, "[StopStationProcess] 厂站 %s 进程未运行", stationId.c_str());
        }

        return 0;

    } catch (...) {
        m_pLogFile->FormatAdd(CLogFile::error, "[StopStationProcess] 停止厂站 %s 进程异常", stationId.c_str());
        return -1;
    }
}

/*************************************************************
 函 数 名:   UpdateStationCommStatus
 功能概要:   更新厂站通信状态
 返 回 值:   void
**************************************************************/
void CTaskMngr::UpdateStationCommStatus(const std::string& stationId, int newStatus)
{
    try {
        // 更新本地状态映射
        m_mapFntSta[stationId] = newStatus;

        // 同步到服务器在线管理器
        if (m_bSrvOnlineMngrEnabled && m_pSrvOnlineMngr != NULL) {
            stXJSubstationLoadStatus status;
            memset(&status, 0, sizeof(status));
            strncpy(status.chStationID, stationId.c_str(), sizeof(status.chStationID) - 1);
            status.iStatus = newStatus;  // -1-未知, 0-未挂载, 1-已挂载

            char errorMsg[256] = "";
            int ret = m_pSrvOnlineMngr->SetStationLoadStatus(status, errorMsg);
            if (ret != 0) {
                m_pLogFile->FormatAdd(CLogFile::error, "[UpdateStationCommStatus] 更新厂站 %s 状态失败: %s",
                                     stationId.c_str(), errorMsg);
            } else {
                m_pLogFile->FormatAdd(CLogFile::trace, "[UpdateStationCommStatus] 更新厂站 %s 状态: %d",
                                     stationId.c_str(), newStatus);
            }
        }

    } catch (...) {
        m_pLogFile->FormatAdd(CLogFile::error, "[UpdateStationCommStatus] 更新厂站 %s 状态异常", stationId.c_str());
    }
}